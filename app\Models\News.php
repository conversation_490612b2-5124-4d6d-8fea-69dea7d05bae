<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class News extends Model
{
    protected $table = 'news';

    protected $fillable = [
        'title',
        'description',
        'content',
        'image',
        'type',
        'status'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Scope for published news (using status = 'active')
    public function scopePublished($query)
    {
        return $query->where('status', 'active');
    }

    // Scope for specific language (ignore for now since no language column)
    public function scopeLanguage($query, $language)
    {
        return $query; // Return all records regardless of language
    }

    // Scope for featured news (use type = 'features')
    public function scopeFeatured($query)
    {
        return $query->where('type', 'features');
    }

    // Get published attribute (map status to published)
    public function getPublishedAttribute()
    {
        return $this->status === 'active';
    }

    // Set published attribute (map published to status)
    public function setPublishedAttribute($value)
    {
        $this->attributes['status'] = $value ? 'active' : 'inactive';
    }

    // Get formatted published date
    public function getFormattedDateAttribute()
    {
        return $this->created_at->format('M d, Y');
    }

    // Get excerpt attribute (use description or truncated content)
    public function getExcerptAttribute()
    {
        return $this->description ?: \Str::limit(strip_tags($this->content), 150);
    }

    // Get author attribute (default value)
    public function getAuthorAttribute()
    {
        return 'L2GVE Team';
    }

    // Get published_at attribute (use created_at when published)
    public function getPublishedAtAttribute()
    {
        return $this->published ? $this->created_at : null;
    }
}
