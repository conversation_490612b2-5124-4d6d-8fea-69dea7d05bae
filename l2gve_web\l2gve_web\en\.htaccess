# English directory configuration
DirectoryIndex index.php index.html

# Enable rewrite rules for clean URLs
RewriteEngine On

# Redirect .html URLs to clean URLs
RewriteCond %{THE_REQUEST} \s/l2gve_web/l2gve_web/en/([^.\s?]+)\.html [NC]
RewriteRule ^ /l2gve_web/l2gve_web/en/%1? [NC,L,R=302]

# Serve index.html for index URLs
RewriteRule ^index/?$ index.html [NC,L]

# Remove .html extension from other URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.html -f
RewriteRule ^([^\.]+)/?$ $1.html [NC,L,QSA]

# Simple caching for this directory
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header set X-Powered-By "L2GVE-English"
    Header set Cache-Control "public, max-age=3600"
</IfModule>
