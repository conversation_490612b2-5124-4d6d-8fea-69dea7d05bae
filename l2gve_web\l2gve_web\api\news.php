<?php
// News API with database connection
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Include database config
require_once '../config/database.php';

// Fallback data in case database is not available
function getFallbackData() {
    return [
        [
            'id' => 1,
            'title' => 'THÔNG TIN DỰ ÁN INTERLUDE X30',
            'description' => 'Các khía cạnh game được chế tác cẩn thận. Shop đến B-grade. Buffer với tùy chọn buff premium...',
            'desc' => 'Các khía cạnh game được chế tác cẩn thận. Shop đến B-grade. Buffer với tùy chọn buff premium...',
            'content' => 'Nội dung đầy đủ ở đây...',
            'image' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'img' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'type' => 'news',
            'status' => 'active',
            'created_at' => '2024-08-01 12:00:00',
            'date' => '2024-08-01 12:00:00'
        ],
        [
            'id' => 2,
            'title' => 'BETA TEST CHÍNH THỨC - 12 THÁNG 8',
            'description' => 'Ngày 12 tháng 8 bắt đầu beta test mở của máy chủ L2GVE...',
            'desc' => 'Ngày 12 tháng 8 bắt đầu beta test mở của máy chủ L2GVE...',
            'content' => 'Nội dung đầy đủ ở đây...',
            'image' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'img' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'type' => 'promotions',
            'status' => 'active',
            'created_at' => '2024-08-12 10:00:00',
            'date' => '2024-08-12 10:00:00'
        ]
    ];
}

// Main handler with database connection
try {
    $method = $_SERVER['REQUEST_METHOD'];
    $id = $_GET['id'] ?? null;
    $type = $_GET['type'] ?? null;
    $limit = min((int)($_GET['limit'] ?? 10), 50);

    // Try to connect to database
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Handle different HTTP methods
    switch ($method) {
        case 'POST':
            handleCreate($conn);
            break;
        case 'PUT':
            handleUpdate($conn, $id);
            break;
        case 'DELETE':
            handleDelete($conn, $id);
            break;
        case 'GET':
        default:
            handleGet($conn, $id, $type, $limit);
            break;
    }

} catch (Exception $e) {
    // If database fails, use fallback data for GET requests only
    error_log('Database error in news API: ' . $e->getMessage());

    if ($method === 'GET') {
        handleGetFallback($id, $type, $limit);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Database connection failed']);
    }
}

// Handle GET requests
function handleGet($conn, $id, $type, $limit) {

    if ($id) {
        // Get single news item
        $stmt = $conn->prepare("SELECT id, title, description, content, image, type, status, created_at, updated_at FROM news WHERE id = ? AND status = 'active'");
        $stmt->execute([$id]);
        $result = $stmt->fetch();

        if (!$result) {
            http_response_code(404);
            echo json_encode(['error' => 'News not found']);
            return;
        }

        // Format the result for consistency
        $result['img'] = $result['image'];
        $result['desc'] = $result['description'];
        $result['date'] = $result['created_at'];

        echo json_encode($result);

    } else {
        // Get multiple news items
        $whereClause = "WHERE status = 'active'";
        $params = [];

        if ($type && $type !== 'all') {
            $whereClause .= " AND type = ?";
            $params[] = $type;
        }

        $sql = "SELECT id, title, description, content, image, type, status, created_at, updated_at
                FROM news $whereClause
                ORDER BY created_at DESC
                LIMIT " . (int)$limit;

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll();

        // Format results for consistency
        $formattedResults = array_map(function($item) {
            $item['img'] = $item['image'];
            $item['desc'] = $item['description'];
            $item['date'] = $item['created_at'];
            return $item;
        }, $results);

        echo json_encode($formattedResults);
    }
}

// Handle POST requests (Create)
function handleCreate($conn) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['title']) || !isset($input['description'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }

    $stmt = $conn->prepare("INSERT INTO news (title, description, content, image, type) VALUES (?, ?, ?, ?, ?)");
    $result = $stmt->execute([
        $input['title'],
        $input['description'],
        $input['content'] ?? '',
        $input['image'] ?? '',
        $input['type'] ?? 'news'
    ]);

    if ($result) {
        $newId = $conn->lastInsertId();
        echo json_encode(['success' => true, 'id' => $newId, 'message' => 'News created successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create news']);
    }
}

// Handle PUT requests (Update)
function handleUpdate($conn, $id) {
    if (!$id) {
        http_response_code(400);
        echo json_encode(['error' => 'ID is required for update']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON data']);
        return;
    }

    $stmt = $conn->prepare("UPDATE news SET title = ?, description = ?, content = ?, image = ?, type = ? WHERE id = ?");
    $result = $stmt->execute([
        $input['title'] ?? '',
        $input['description'] ?? '',
        $input['content'] ?? '',
        $input['image'] ?? '',
        $input['type'] ?? 'news',
        $id
    ]);

    if ($result) {
        echo json_encode(['success' => true, 'message' => 'News updated successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update news']);
    }
}

// Handle DELETE requests
function handleDelete($conn, $id) {
    if (!$id) {
        http_response_code(400);
        echo json_encode(['error' => 'ID is required for delete']);
        return;
    }

    $stmt = $conn->prepare("DELETE FROM news WHERE id = ?");
    $result = $stmt->execute([$id]);

    if ($result) {
        echo json_encode(['success' => true, 'message' => 'News deleted successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete news']);
    }
}

// Handle GET with fallback data
function handleGetFallback($id, $type, $limit) {
    $fallbackData = getFallbackData();

    if ($id) {
        $newsData = null;
        foreach ($fallbackData as $item) {
            if ($item['id'] == $id) {
                $newsData = $item;
                break;
            }
        }
        if (!$newsData) {
            http_response_code(404);
            echo json_encode(['error' => 'News not found']);
            return;
        }
        echo json_encode($newsData);
    } else {
        $newsData = $fallbackData;
        if ($type && $type !== 'all') {
            $newsData = array_filter($newsData, function($item) use ($type) {
                return $item['type'] === $type;
            });
            $newsData = array_values($newsData);
        }
        $newsData = array_slice($newsData, 0, $limit);
        echo json_encode($newsData);
    }
}
?>

