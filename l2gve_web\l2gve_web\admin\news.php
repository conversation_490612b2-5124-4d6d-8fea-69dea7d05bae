<!DOCTYPE html>
<html>
<head>
    <title>News Management</title>
    <meta charset="utf-8">

    <!-- Quill Rich Text Editor -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .news-list { margin-top: 30px; }
        .news-item { border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; }
        .news-actions {
            margin-top: 10px;
        }
        .btn-edit, .btn-delete {
            padding: 5px 15px;
            margin-right: 10px;
            border: none;
            cursor: pointer;
            border-radius: 3px;
        }
        .btn-edit {
            background: #28a745;
            color: white;
        }
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        .btn-edit:hover {
            background: #218838;
        }
        .btn-delete:hover {
            background: #c82333;
        }

        /* Rich Text Editor Styles */
        .editor-container {
            margin-bottom: 15px;
        }

        .editor-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        #editor {
            height: 200px;
            background: white;
            border: 1px solid #ddd;
        }

        .ql-toolbar {
            border-top: 1px solid #ddd;
            border-left: 1px solid #ddd;
            border-right: 1px solid #ddd;
        }

        .ql-container {
            border-bottom: 1px solid #ddd;
            border-left: 1px solid #ddd;
            border-right: 1px solid #ddd;
        }

        .preview-container {
            margin-top: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            background: #f9f9f9;
            border-radius: 4px;
        }

        .preview-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .preview-content {
            color: #666;
            line-height: 1.6;
        }

        .editor-tabs {
            display: flex;
            margin-bottom: 10px;
        }

        .editor-tab {
            padding: 8px 16px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }

        .editor-tab.active {
            background: white;
            border-bottom: 1px solid white;
        }

        .editor-panel {
            display: none;
        }

        .editor-panel.active {
            display: block;
        }

        .html-editor {
            width: 100%;
            height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            padding: 10px;
            border: 1px solid #ddd;
            resize: vertical;
        }

        /* Image Upload Styles */
        .image-upload-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }

        .upload-tabs {
            display: flex;
            background: #f0f0f0;
        }

        .upload-tab {
            flex: 1;
            padding: 10px;
            border: none;
            background: #e6f3ff;
            color: #007cba;
            font-weight: 500;
            cursor: pointer;
            border-right: 1px solid #007cba;
            transition: all 0.3s ease;
        }

        .upload-tab:last-child {
            border-right: none;
        }

        .upload-tab.active {
            background: #007cba;
            color: white;
            font-weight: bold;
        }

        .upload-tab:hover {
            background: #e6f3ff;
            color: #007cba;
        }

        .upload-tab.active:hover {
            background: #005a8b;
            color: white;
        }

        .upload-panel {
            padding: 15px;
            display: none;
        }

        .upload-panel.active {
            display: block;
        }

        .upload-panel input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #007cba;
            border-radius: 4px;
            background: #f0f8ff;
            color: #007cba;
            font-weight: 500;
        }

        .upload-panel input[type="file"]:hover {
            border-color: #005a8b;
            background: #e6f3ff;
        }

        .upload-panel input[type="file"]:focus {
            outline: none;
            border-color: #005a8b;
            box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
        }

        .upload-progress {
            margin-top: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #007cba;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            display: block;
            text-align: center;
            margin-top: 5px;
            font-size: 12px;
        }

        .image-preview {
            margin-top: 10px;
            position: relative;
            display: inline-block;
        }

        .btn-remove {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #dc3545;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 12px;
            line-height: 1;
        }
    </style>
</head>
<body>
    <h1>Quản lý tin tức</h1>
    
    <form id="newsForm">
        <div class="form-group">
            <label>Tiêu đề:</label>
            <input type="text" id="title" required>
        </div>
        
        <div class="form-group">
            <label>Mô tả ngắn:</label>
            <textarea id="description" rows="3" required></textarea>
        </div>
        
        <div class="form-group">
            <div class="editor-container">
                <label class="editor-label">Nội dung:</label>

                <div class="editor-tabs">
                    <div class="editor-tab active" onclick="switchEditorTab('visual')">Visual</div>
                    <div class="editor-tab" onclick="switchEditorTab('html')">HTML</div>
                    <div class="editor-tab" onclick="switchEditorTab('preview')">Preview</div>
                </div>

                <div id="visual-panel" class="editor-panel active">
                    <div id="editor"></div>
                </div>

                <div id="html-panel" class="editor-panel">
                    <textarea id="html-editor" class="html-editor" placeholder="Nhập HTML code..."></textarea>
                </div>

                <div id="preview-panel" class="editor-panel">
                    <div class="preview-container">
                        <div class="preview-title">Preview:</div>
                        <div id="preview-content" class="preview-content">Nội dung sẽ hiển thị ở đây...</div>
                    </div>
                </div>

                <textarea id="content" style="display: none;"></textarea>
            </div>
        </div>
        
        <div class="form-group">
            <label>Hình ảnh:</label>
            <div class="image-upload-container">
                <div class="upload-tabs">
                    <button type="button" class="upload-tab active" onclick="switchUploadTab('url')">URL</button>
                    <button type="button" class="upload-tab" onclick="switchUploadTab('file')">Upload File</button>
                </div>

                <div id="url-upload" class="upload-panel active">
                    <input type="url" id="image" placeholder="https://example.com/image.jpg">
                </div>

                <div id="file-upload" class="upload-panel">
                    <input type="file" id="imageFile" accept="image/*" onchange="handleImageUpload(this)">
                    <div class="upload-progress" id="uploadProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <span class="progress-text" id="progressText">0%</span>
                    </div>
                </div>

                <div class="image-preview" id="imagePreview" style="display: none;">
                    <img id="previewImg" src="" alt="Preview" style="max-width: 200px; max-height: 150px;">
                    <button type="button" onclick="removeImage()" class="btn-remove">×</button>
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <label>Loại tin:</label>
            <select id="type">
                <option value="news">Tin tức</option>
                <option value="promotions">Khuyến mãi</option>
                <option value="updates">Cập nhật</option>
                <option value="features">Tính năng</option>
            </select>
        </div>
        
        <button type="submit">Tạo tin tức</button>
    </form>
    
    <div class="news-list" id="newsList"></div>

    <script>
        let editingNewsId = null;
        let quill = null;

        // Initialize Quill editor
        function initEditor() {
            quill = new Quill('#editor', {
                theme: 'snow',
                modules: {
                    toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'align': [] }],
                        ['link', 'image', 'video'],
                        ['blockquote', 'code-block'],
                        ['clean']
                    ]
                },
                placeholder: 'Nhập nội dung tin tức...'
            });

            // Sync content between editors
            quill.on('text-change', function() {
                const html = quill.root.innerHTML;
                document.getElementById('html-editor').value = html;
                document.getElementById('content').value = html;
                updatePreview();
            });
        }

        // Switch between editor tabs
        function switchEditorTab(tab) {
            // Remove active class from all tabs
            document.querySelectorAll('.editor-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.editor-panel').forEach(p => p.classList.remove('active'));

            // Add active class to selected tab
            event.target.classList.add('active');
            document.getElementById(tab + '-panel').classList.add('active');

            if (tab === 'html') {
                // Sync from visual to HTML
                const html = quill.root.innerHTML;
                document.getElementById('html-editor').value = html;
            } else if (tab === 'visual') {
                // Sync from HTML to visual
                const html = document.getElementById('html-editor').value;
                quill.root.innerHTML = html;
                document.getElementById('content').value = html;
            } else if (tab === 'preview') {
                updatePreview();
            }
        }

        // Update preview
        function updatePreview() {
            const content = quill.root.innerHTML || document.getElementById('html-editor').value;
            document.getElementById('preview-content').innerHTML = content;
        }

        // HTML editor sync
        document.addEventListener('DOMContentLoaded', function() {
            initEditor();

            // HTML editor change handler
            document.getElementById('html-editor').addEventListener('input', function() {
                const html = this.value;
                document.getElementById('content').value = html;
                updatePreview();
            });
        });

        // Image upload functions
        function switchUploadTab(tab) {
            // Remove active class from all tabs
            document.querySelectorAll('.upload-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.upload-panel').forEach(p => p.classList.remove('active'));

            // Add active class to selected tab
            event.target.classList.add('active');
            document.getElementById(tab + '-upload').classList.add('active');
        }

        function handleImageUpload(input) {
            const file = input.files[0];
            if (!file) return;

            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Vui lòng chọn file hình ảnh!');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('File quá lớn! Vui lòng chọn file nhỏ hơn 5MB.');
                return;
            }

            // Show progress
            const progressContainer = document.getElementById('uploadProgress');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressContainer.style.display = 'block';

            // Create FormData
            const formData = new FormData();
            formData.append('image', file);

            // Upload with progress
            const xhr = new XMLHttpRequest();

            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressFill.style.width = percentComplete + '%';
                    progressText.textContent = Math.round(percentComplete) + '%';
                }
            });

            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            // Set image URL
                            document.getElementById('image').value = response.url;

                            // Show preview
                            showImagePreview(response.url);

                            // Hide progress
                            progressContainer.style.display = 'none';

                            alert('Upload thành công!');
                        } else {
                            alert('Lỗi upload: ' + (response.error || 'Unknown error'));
                        }
                    } catch (e) {
                        alert('Lỗi xử lý response: ' + e.message);
                    }
                } else {
                    alert('Lỗi upload: HTTP ' + xhr.status);
                }

                // Reset progress
                progressFill.style.width = '0%';
                progressText.textContent = '0%';
                progressContainer.style.display = 'none';
            });

            xhr.addEventListener('error', function() {
                alert('Lỗi kết nối khi upload!');
                progressContainer.style.display = 'none';
            });

            // Send request
            xhr.open('POST', '../api/upload-image.php');
            xhr.send(formData);
        }

        function showImagePreview(url) {
            const preview = document.getElementById('imagePreview');
            const img = document.getElementById('previewImg');

            img.src = url;
            preview.style.display = 'block';
        }

        function removeImage() {
            document.getElementById('image').value = '';
            document.getElementById('imageFile').value = '';
            document.getElementById('imagePreview').style.display = 'none';
        }

        // Tạo tin tức mới
        document.getElementById('newsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const newsData = {
                title: document.getElementById('title').value,
                description: document.getElementById('description').value,
                content: document.getElementById('content').value,
                image: document.getElementById('image').value,
                type: document.getElementById('type').value
            };
            
            const method = editingNewsId ? 'PUT' : 'POST';
            const url = editingNewsId ? `../api/news.php?id=${editingNewsId}` : '../api/news.php';
            
            fetch(url, {
                method: method,
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(newsData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    alert(editingNewsId ? 'Tin tức đã được cập nhật!' : 'Tin tức đã được tạo!');
                    loadNews();
                    resetForm();
                } else {
                    alert('Lỗi: ' + (data.error || 'Không thể lưu tin tức'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Lỗi kết nối: ' + error.message);
            });
        });

        // Load danh sách tin tức với nút sửa/xóa
        function loadNews() {
            fetch('../api/news.php')
            .then(response => response.json())
            .then(data => {
                const newsList = document.getElementById('newsList');
                newsList.innerHTML = '<h2>Danh sách tin tức</h2>';
                
                data.forEach(news => {
                    newsList.innerHTML += `
                        <div class="news-item">
                            <h3>${news.title}</h3>
                            <p><strong>Loại:</strong> ${news.type}</p>
                            <p><strong>Ngày:</strong> ${news.date}</p>
                            <p>${news.desc}</p>
                            <div class="news-actions">
                                <button onclick="editNews(${news.id})" class="btn-edit">Sửa</button>
                                <button onclick="deleteNews(${news.id})" class="btn-delete">Xóa</button>
                            </div>
                        </div>
                    `;
                });
            });
        }

        // Sửa tin tức
        function editNews(id) {
            fetch(`../api/news.php?id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (!data.error) {
                    editingNewsId = id;
                    document.getElementById('title').value = data.title;
                    document.getElementById('description').value = data.desc;
                    document.getElementById('content').value = data.content || '';
                    document.getElementById('image').value = data.img || '';
                    document.getElementById('type').value = data.type;

                    // Load content into editors
                    const content = data.content || '';
                    if (quill) {
                        quill.root.innerHTML = content;
                    }
                    document.getElementById('html-editor').value = content;
                    updatePreview();

                    // Switch to visual tab
                    document.querySelectorAll('.editor-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.editor-panel').forEach(p => p.classList.remove('active'));
                    document.querySelector('.editor-tab').classList.add('active');
                    document.getElementById('visual-panel').classList.add('active');

                    document.querySelector('button[type="submit"]').textContent = 'Cập nhật tin tức';
                    window.scrollTo(0, 0);
                }
            });
        }

        // Xóa tin tức
        function deleteNews(id) {
            if (confirm('Bạn có chắc chắn muốn xóa tin tức này?')) {
                fetch(`../api/news.php?id=${id}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert('Tin tức đã được xóa!');
                        loadNews();
                    } else {
                        alert('Lỗi: ' + (data.error || 'Không thể xóa tin tức'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Lỗi kết nối: ' + error.message);
                });
            }
        }

        // Reset form
        function resetForm() {
            editingNewsId = null;
            document.getElementById('newsForm').reset();

            // Reset Quill editor
            if (quill) {
                quill.setContents([]);
            }

            // Reset HTML editor
            document.getElementById('html-editor').value = '';

            // Reset preview
            document.getElementById('preview-content').innerHTML = 'Nội dung sẽ hiển thị ở đây...';

            // Switch back to visual tab
            document.querySelectorAll('.editor-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.editor-panel').forEach(p => p.classList.remove('active'));
            document.querySelector('.editor-tab').classList.add('active');
            document.getElementById('visual-panel').classList.add('active');

            document.querySelector('button[type="submit"]').textContent = 'Tạo tin tức';
        }

        // Load tin tức khi trang được tải
        loadNews();
    </script>
</body>
</html>

