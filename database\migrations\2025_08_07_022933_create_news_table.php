<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news_laravel', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description'); // Sử dụng description như database hiện tại
            $table->longText('content')->nullable();
            $table->string('image')->nullable();
            $table->enum('type', ['news', 'promotions', 'updates', 'features'])->default('news');
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->string('language', 2)->default('en'); // 'en' or 'vn'
            $table->boolean('featured')->default(false);
            $table->timestamps();

            $table->index(['language', 'status', 'created_at']);
            $table->index(['type', 'status']);
            $table->index(['featured', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news_laravel');
    }
};
