<?php
declare(strict_types=1);

$allowedHosts = ['127.0.0.1', '::1', 'localhost'];
$allowedDomains = ['l2gve.local', 'localhost'];

$isAllowed = in_array($_SERVER['REMOTE_ADDR'], $allowedHosts) || 
             in_array($_SERVER['HTTP_HOST'], $allowedDomains);

if (!$isAllowed) {
    die('Access denied');
}

echo "<h2>Clear Cache Tool</h2>";
echo "<pre>";

require_once '../config/autoload.php';
require_once '../config/cache.php';

$cache = CacheManager::getInstance();

try {
    $cache->clear();
    echo "✓ Cache cleared successfully!\n";
    echo "All cached pages have been removed.\n";
} catch (Exception $e) {
    echo "✗ Error clearing cache: " . $e->getMessage() . "\n";
}

echo "</pre>";
echo "<p><a href='warm_cache_web.php'>🔥 Warm Cache Again</a> | <a href='/admin/'>← Back to Admin</a></p>";

