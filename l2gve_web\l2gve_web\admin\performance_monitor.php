<?php
declare(strict_types=1);

// Security check
$allowedHosts = ['127.0.0.1', '::1', 'localhost'];
$allowedDomains = ['l2gve.local', 'localhost'];

$isAllowed = in_array($_SERVER['REMOTE_ADDR'], $allowedHosts) || 
             in_array($_SERVER['HTTP_HOST'], $allowedDomains);

if (!$isAllowed) {
    die('Access denied');
}

require_once __DIR__ . '/../config/jit_optimizer.php';
require_once __DIR__ . '/../config/cache_manager.php';

$optimizer = new JITOptimizer();
$cache = CacheManager::getInstance();

// Handle actions
$action = $_GET['action'] ?? 'dashboard';

switch ($action) {
    case 'warmup':
        $optimizer->warmupJIT();
        $message = "JIT warmup completed successfully!";
        break;
    case 'clear_cache':
        $cache->clear();
        $message = "Cache cleared successfully!";
        break;
    case 'opcache_reset':
        if (function_exists('opcache_reset')) {
            opcache_reset();
            $message = "OPcache reset successfully!";
        } else {
            $message = "OPcache not available!";
        }
        break;
}

$metrics = $optimizer->getPerformanceMetrics();
$jitStatus = $optimizer->initializeJIT();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>L2GVE Performance Monitor</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h3 { color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .metric { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee; }
        .metric:last-child { border-bottom: none; }
        .metric-label { font-weight: 600; color: #555; }
        .metric-value { color: #2c3e50; font-family: monospace; }
        .status-good { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-error { color: #e74c3c; }
        .btn { display: inline-block; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 5px; border: none; cursor: pointer; }
        .btn:hover { background: #2980b9; }
        .btn-danger { background: #e74c3c; }
        .btn-danger:hover { background: #c0392b; }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .message { padding: 15px; margin: 10px 0; border-radius: 5px; background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .recommendations { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .progress-bar { width: 100%; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #3498db, #2ecc71); transition: width 0.3s ease; }
        .auto-refresh { position: fixed; top: 20px; right: 20px; background: #34495e; color: white; padding: 10px; border-radius: 5px; }
    </style>
    <script>
        // Auto-refresh every 30 seconds
        let autoRefresh = false;
        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const btn = document.getElementById('autoRefreshBtn');
            btn.textContent = autoRefresh ? 'Stop Auto Refresh' : 'Start Auto Refresh';
            btn.className = autoRefresh ? 'btn btn-danger' : 'btn btn-success';
            
            if (autoRefresh) {
                setTimeout(function refresh() {
                    if (autoRefresh) {
                        location.reload();
                        setTimeout(refresh, 30000);
                    }
                }, 30000);
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 L2GVE Performance Monitor</h1>
            <p>PHP 8.4 Optimization Dashboard</p>
            <div style="margin-top: 15px;">
                <a href="?action=warmup" class="btn btn-success">🔥 Warmup JIT</a>
                <a href="?action=clear_cache" class="btn">🗑️ Clear Cache</a>
                <a href="?action=opcache_reset" class="btn">♻️ Reset OPcache</a>
                <button id="autoRefreshBtn" class="btn btn-success" onclick="toggleAutoRefresh()">Start Auto Refresh</button>
            </div>
        </div>

        <?php if (isset($message)): ?>
            <div class="message"><?= htmlspecialchars($message) ?></div>
        <?php endif; ?>

        <div class="grid">
            <!-- System Status -->
            <div class="card">
                <h3>📊 System Status</h3>
                <div class="metric">
                    <span class="metric-label">PHP Version:</span>
                    <span class="metric-value"><?= PHP_VERSION ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">OPcache:</span>
                    <span class="metric-value <?= $jitStatus['opcache_enabled'] ? 'status-good' : 'status-error' ?>">
                        <?= $jitStatus['opcache_enabled'] ? '✅ Enabled' : '❌ Disabled' ?>
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">JIT Compiler:</span>
                    <span class="metric-value <?= $jitStatus['jit_enabled'] ? 'status-good' : 'status-error' ?>">
                        <?= $jitStatus['jit_enabled'] ? '✅ Enabled' : '❌ Disabled' ?>
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">JIT Mode:</span>
                    <span class="metric-value"><?= $jitStatus['jit_mode'] ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">JIT Buffer:</span>
                    <span class="metric-value"><?= number_format($jitStatus['jit_buffer_size'] / 1024 / 1024, 0) ?>MB</span>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="card">
                <h3>⚡ Performance Metrics</h3>
                <div class="metric">
                    <span class="metric-label">Execution Time:</span>
                    <span class="metric-value"><?= $metrics['execution_time'] ?>ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory Usage:</span>
                    <span class="metric-value"><?= $metrics['memory_usage'] ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Peak Memory:</span>
                    <span class="metric-value"><?= $metrics['peak_memory'] ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Server Load:</span>
                    <span class="metric-value"><?= function_exists('sys_getloadavg') ? implode(', ', array_map(fn($x) => number_format($x, 2), sys_getloadavg())) : 'N/A' ?></span>
                </div>
            </div>

            <!-- OPcache Statistics -->
            <?php if ($metrics['opcache_stats']['enabled']): ?>
            <div class="card">
                <h3>🗄️ OPcache Statistics</h3>
                <div class="metric">
                    <span class="metric-label">Memory Usage:</span>
                    <span class="metric-value"><?= $metrics['opcache_stats']['memory_usage'] ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory Free:</span>
                    <span class="metric-value"><?= $metrics['opcache_stats']['memory_free'] ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Cached Scripts:</span>
                    <span class="metric-value"><?= $metrics['opcache_stats']['cached_scripts'] ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Hit Rate:</span>
                    <span class="metric-value <?= $metrics['opcache_stats']['hit_rate'] > 90 ? 'status-good' : ($metrics['opcache_stats']['hit_rate'] > 70 ? 'status-warning' : 'status-error') ?>">
                        <?= $metrics['opcache_stats']['hit_rate'] ?>%
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">Cache Status:</span>
                    <span class="metric-value <?= $metrics['opcache_stats']['cache_full'] ? 'status-warning' : 'status-good' ?>">
                        <?= $metrics['opcache_stats']['cache_full'] ? '⚠️ Full' : '✅ OK' ?>
                    </span>
                </div>
                
                <!-- Hit Rate Progress Bar -->
                <div style="margin-top: 15px;">
                    <label>Hit Rate Progress:</label>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?= $metrics['opcache_stats']['hit_rate'] ?>%"></div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- JIT Statistics -->
            <?php if ($metrics['jit_stats']['available']): ?>
            <div class="card">
                <h3>🔥 JIT Statistics</h3>
                <div class="metric">
                    <span class="metric-label">Status:</span>
                    <span class="metric-value <?= $metrics['jit_stats']['enabled'] ? 'status-good' : 'status-error' ?>">
                        <?= $metrics['jit_stats']['enabled'] ? '✅ Enabled' : '❌ Disabled' ?>
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">Kind:</span>
                    <span class="metric-value"><?= $metrics['jit_stats']['kind'] ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Optimization Level:</span>
                    <span class="metric-value"><?= $metrics['jit_stats']['opt_level'] ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Optimization Flags:</span>
                    <span class="metric-value"><?= $metrics['jit_stats']['opt_flags'] ?></span>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Recommendations -->
        <?php if (!empty($jitStatus['recommendations'])): ?>
        <div class="recommendations">
            <h3>💡 Optimization Recommendations</h3>
            <ul>
                <?php foreach ($jitStatus['recommendations'] as $recommendation): ?>
                    <li><?= htmlspecialchars($recommendation) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <!-- Full Report -->
        <div class="card" style="margin-top: 20px;">
            <h3>📋 Full Optimization Report</h3>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;"><?= htmlspecialchars($optimizer->generateOptimizationReport()) ?></pre>
        </div>
    </div>

    <div class="auto-refresh" id="refreshStatus">
        Last updated: <?= date('H:i:s') ?>
    </div>
</body>
</html>
