@extends('admin.layout')

@section('title', 'News Management')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-newspaper me-2"></i>News Management
    </h1>
    <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Create New Article
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <select name="type" class="form-select">
                    <option value="">All Types</option>
                    <option value="news" {{ request('type') === 'news' ? 'selected' : '' }}>News</option>
                    <option value="promotions" {{ request('type') === 'promotions' ? 'selected' : '' }}>Promotions</option>
                    <option value="features" {{ request('type') === 'features' ? 'selected' : '' }}>Features</option>
                </select>
            </div>
            <div class="col-md-3">
                <select name="language" class="form-select">
                    <option value="">All Languages</option>
                    <option value="en" {{ request('language') === 'en' ? 'selected' : '' }}>English</option>
                    <option value="vn" {{ request('language') === 'vn' ? 'selected' : '' }}>Vietnamese</option>
                </select>
            </div>
            <div class="col-md-3">
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="published" {{ request('status') === 'published' ? 'selected' : '' }}>Published</option>
                    <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-outline-light">
                    <i class="fas fa-filter me-1"></i>Filter
                </button>
                <a href="{{ route('admin.news.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- News Table -->
<div class="card">
    <div class="card-body">
        @if($news->count() > 0)
            <div class="table-responsive">
                <table class="table table-dark table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Language</th>
                            <th>Status</th>
                            <th>Featured</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($news as $item)
                            <tr>
                                <td>{{ $item->id }}</td>
                                <td>
                                    <div class="fw-bold">{{ Str::limit($item->title, 40) }}</div>
                                    @if($item->excerpt)
                                        <small class="text-muted">{{ Str::limit($item->excerpt, 60) }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ ucfirst($item->type) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ strtoupper($item->language) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $item->published ? 'success' : 'warning' }}">
                                        {{ $item->published ? 'Published' : 'Draft' }}
                                    </span>
                                </td>
                                <td>
                                    @if($item->featured)
                                        <i class="fas fa-star text-warning"></i>
                                    @else
                                        <i class="far fa-star text-muted"></i>
                                    @endif
                                </td>
                                <td>
                                    <small>{{ $item->created_at->format('M d, Y') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.news.show', $item->id) }}" class="btn btn-outline-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.news.edit', $item->id) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ route('admin.news.destroy', $item->id) }}" class="d-inline" 
                                              onsubmit="return confirm('Are you sure you want to delete this article?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $news->withQueryString()->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No news articles found</h4>
                <p class="text-muted">Start by creating your first article.</p>
                <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Article
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@section('styles')
<style>
.table-dark {
    --bs-table-color: #fff;
    --bs-table-bg: rgba(255, 255, 255, 0.05);
    --bs-table-border-color: rgba(255, 255, 255, 0.1);
    --bs-table-striped-bg: rgba(255, 255, 255, 0.02);
    --bs-table-hover-bg: rgba(255, 255, 255, 0.1);
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.pagination {
    --bs-pagination-bg: rgba(255, 255, 255, 0.1);
    --bs-pagination-border-color: rgba(255, 255, 255, 0.2);
    --bs-pagination-color: #fff;
    --bs-pagination-hover-bg: rgba(255, 255, 255, 0.2);
    --bs-pagination-hover-border-color: rgba(255, 255, 255, 0.3);
    --bs-pagination-active-bg: var(--accent-color);
    --bs-pagination-active-border-color: var(--accent-color);
}
</style>
@endsection
