// Service Worker for L2GVE - Advanced Caching Strategy
const CACHE_NAME = 'l2gve-v2.0';
const STATIC_CACHE = 'l2gve-static-v2.0';
const DYNAMIC_CACHE = 'l2gve-dynamic-v2.0';
const IMAGE_CACHE = 'l2gve-images-v2.0';

// Resources to cache immediately
const STATIC_ASSETS = [
    '/',
    '/en/',
    '/vn/',
    '/template/site/l2gve/css/critical.css',
    '/template/site/l2gve/js/app.js',
    '/template/site/l2gve/libs/jquery/jquery-3.4.1.min.js',
    '/template/site/l2gve/fonts/beaufortforlol/fonts.css',
    '/template/site/l2gve/images/favicon/favicon-32x32.png',
    '/bypass-protection.js'
];

// Cache strategies
const CACHE_STRATEGIES = {
    // Cache first for static assets
    static: [
        /\.css$/,
        /\.js$/,
        /\.woff2?$/,
        /\.ttf$/,
        /\.eot$/,
        /favicon/
    ],
    
    // Network first for API and dynamic content
    networkFirst: [
        /\/api\//,
        /\.php$/,
        /\/admin\//
    ],
    
    // Cache first for images with fallback
    images: [
        /\.jpg$/,
        /\.jpeg$/,
        /\.png$/,
        /\.gif$/,
        /\.webp$/,
        /\.svg$/
    ]
};

// Install event - cache static assets
self.addEventListener('install', event => {
    console.log('SW: Installing...');
    
    event.waitUntil(
        Promise.all([
            caches.open(STATIC_CACHE).then(cache => {
                console.log('SW: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            }),
            self.skipWaiting()
        ])
    );
});

// Activate event - clean old caches
self.addEventListener('activate', event => {
    console.log('SW: Activating...');
    
    event.waitUntil(
        Promise.all([
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== IMAGE_CACHE) {
                            console.log('SW: Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            self.clients.claim()
        ])
    );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip external requests (except for specific domains)
    if (url.origin !== location.origin && !isAllowedExternalDomain(url.hostname)) {
        return;
    }
    
    event.respondWith(handleRequest(request));
});

async function handleRequest(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    
    try {
        // Determine cache strategy
        if (matchesPatterns(pathname, CACHE_STRATEGIES.static)) {
            return await cacheFirstStrategy(request, STATIC_CACHE);
        }
        
        if (matchesPatterns(pathname, CACHE_STRATEGIES.images)) {
            return await cacheFirstStrategy(request, IMAGE_CACHE, 86400000); // 24 hours
        }
        
        if (matchesPatterns(pathname, CACHE_STRATEGIES.networkFirst)) {
            return await networkFirstStrategy(request, DYNAMIC_CACHE);
        }
        
        // Default: stale while revalidate for HTML pages
        return await staleWhileRevalidateStrategy(request, DYNAMIC_CACHE);
        
    } catch (error) {
        console.error('SW: Error handling request:', error);
        return await handleOffline(request);
    }
}

// Cache first strategy
async function cacheFirstStrategy(request, cacheName, maxAge = null) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
        // Check if cache is still valid
        if (maxAge) {
            const cacheDate = new Date(cachedResponse.headers.get('date'));
            const now = new Date();
            if (now - cacheDate > maxAge) {
                // Cache expired, fetch new version
                try {
                    const networkResponse = await fetch(request);
                    if (networkResponse.ok) {
                        cache.put(request, networkResponse.clone());
                        return networkResponse;
                    }
                } catch (error) {
                    // Network failed, return stale cache
                    return cachedResponse;
                }
            }
        }
        return cachedResponse;
    }
    
    // Not in cache, fetch from network
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
        cache.put(request, networkResponse.clone());
    }
    return networkResponse;
}

// Network first strategy
async function networkFirstStrategy(request, cacheName) {
    const cache = await caches.open(cacheName);
    
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        throw error;
    }
}

// Stale while revalidate strategy
async function staleWhileRevalidateStrategy(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    // Fetch in background
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(() => {
        // Network failed, ignore
    });
    
    // Return cached version immediately if available
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // Wait for network if no cache
    return await fetchPromise;
}

// Handle offline scenarios
async function handleOffline(request) {
    const url = new URL(request.url);
    
    // Try to find a cached version
    const cacheNames = [STATIC_CACHE, DYNAMIC_CACHE, IMAGE_CACHE];
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
    }
    
    // Return offline page for HTML requests
    if (request.headers.get('accept').includes('text/html')) {
        const cache = await caches.open(STATIC_CACHE);
        return await cache.match('/offline.html') || new Response('Offline', { status: 503 });
    }
    
    // Return placeholder for images
    if (matchesPatterns(url.pathname, CACHE_STRATEGIES.images)) {
        return new Response('', { status: 503, statusText: 'Image not available offline' });
    }
    
    throw new Error('Resource not available offline');
}

// Utility functions
function matchesPatterns(pathname, patterns) {
    return patterns.some(pattern => pattern.test(pathname));
}

function isAllowedExternalDomain(hostname) {
    const allowedDomains = [
        'fonts.googleapis.com',
        'fonts.gstatic.com',
        'www.googletagmanager.com',
        'mc.yandex.ru'
    ];
    return allowedDomains.includes(hostname);
}

// Background sync for failed requests
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    // Implement background sync logic here
    console.log('SW: Background sync triggered');
}

// Push notifications (if needed)
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/template/site/l2gve/images/favicon/favicon-192x192.png',
            badge: '/template/site/l2gve/images/favicon/favicon-72x72.png',
            vibrate: [100, 50, 100],
            data: data.data
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click handler
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    event.waitUntil(
        clients.openWindow(event.notification.data.url || '/')
    );
});
