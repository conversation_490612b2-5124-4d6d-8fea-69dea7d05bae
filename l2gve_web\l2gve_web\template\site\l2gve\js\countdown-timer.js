/**
 * Countdown Timer - Time Until OBT (Open Beta Test)
 * Customizable countdown timer with multiple display formats
 */

class CountdownTimer {
    constructor(options = {}) {
        this.options = {
            // Target date/time
            year: options.year || 2025,
            month: options.month || 9, // September
            day: options.day || 1,
            hour: options.hour || 12,
            minute: options.minute || 0,
            second: options.second || 0,
            timeZone: options.timeZone || 7, // GMT+7
            
            // Display options
            format: options.format || 'DHMS', // Days, Hours, Minutes, Seconds
            container: options.container || '[data-countdown]',
            
            // Messages
            endTimeMSG: options.endTimeMSG || 'Server is now live!',
            labels: {
                days: options.labels?.days || 'Days',
                hours: options.labels?.hours || 'Hours', 
                minutes: options.labels?.minutes || 'Minutes',
                seconds: options.labels?.seconds || 'Seconds',
                day: options.labels?.day || 'Day',
                hour: options.labels?.hour || 'Hour',
                minute: options.labels?.minute || 'Minute',
                second: options.labels?.second || 'Second'
            },
            
            // Callbacks
            onTick: options.onTick || null,
            onComplete: options.onComplete || null,
            
            // Auto start
            autoStart: options.autoStart !== false
        };
        
        this.targetDate = null;
        this.interval = null;
        this.isRunning = false;
        
        this.init();
    }
    
    init() {
        this.setTargetDate();
        this.container = document.querySelector(this.options.container);
        
        if (!this.container) {
            console.warn('Countdown container not found:', this.options.container);
            return;
        }
        
        if (this.options.autoStart) {
            this.start();
        }
    }
    
    setTargetDate() {
        // Create target date in specified timezone
        const { year, month, day, hour, minute, second, timeZone } = this.options;
        
        // Create date in UTC then adjust for timezone
        this.targetDate = new Date(Date.UTC(year, month - 1, day, hour - timeZone, minute, second));
    }
    
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.update();
        this.interval = setInterval(() => this.update(), 1000);
    }
    
    stop() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        this.isRunning = false;
    }
    
    restart() {
        this.stop();
        this.start();
    }
    
    update() {
        const now = new Date().getTime();
        const distance = this.targetDate.getTime() - now;
        
        if (distance < 0) {
            this.onCountdownComplete();
            return;
        }
        
        const timeLeft = this.calculateTimeLeft(distance);
        this.render(timeLeft);
        
        if (this.options.onTick) {
            this.options.onTick(timeLeft, distance);
        }
    }
    
    calculateTimeLeft(distance) {
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        return { days, hours, minutes, seconds };
    }
    
    render(timeLeft) {
        const { days, hours, minutes, seconds } = timeLeft;
        const { format, labels } = this.options;
        
        let html = '';
        
        switch (format) {
            case 'DHMS':
                html = `
                    <div class="countdown-item">
                        <span class="countdown-number">${days.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">${days === 1 ? labels.day : labels.days}</span>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-item">
                        <span class="countdown-number">${hours.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">${hours === 1 ? labels.hour : labels.hours}</span>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-item">
                        <span class="countdown-number">${minutes.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">${minutes === 1 ? labels.minute : labels.minutes}</span>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-item">
                        <span class="countdown-number">${seconds.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">${seconds === 1 ? labels.second : labels.seconds}</span>
                    </div>
                `;
                break;
                
            case 'HMS':
                const totalHours = days * 24 + hours;
                html = `
                    <div class="countdown-item">
                        <span class="countdown-number">${totalHours.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">${totalHours === 1 ? labels.hour : labels.hours}</span>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-item">
                        <span class="countdown-number">${minutes.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">${minutes === 1 ? labels.minute : labels.minutes}</span>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-item">
                        <span class="countdown-number">${seconds.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">${seconds === 1 ? labels.second : labels.seconds}</span>
                    </div>
                `;
                break;
                
            case 'COMPACT':
                html = `
                    <span class="countdown-compact">
                        ${days}d ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}
                    </span>
                `;
                break;
                
            default:
                html = `${days}d ${hours}h ${minutes}m ${seconds}s`;
        }
        
        this.container.innerHTML = html;
    }
    
    onCountdownComplete() {
        this.stop();
        this.container.innerHTML = `<div class="countdown-complete">${this.options.endTimeMSG}</div>`;
        
        if (this.options.onComplete) {
            this.options.onComplete();
        }
        
        // Dispatch event
        document.dispatchEvent(new CustomEvent('countdownComplete', {
            detail: { timer: this }
        }));
    }
    
    // Public methods to update target date
    setTarget(year, month, day, hour = 12, minute = 0, second = 0) {
        this.options.year = year;
        this.options.month = month;
        this.options.day = day;
        this.options.hour = hour;
        this.options.minute = minute;
        this.options.second = second;
        
        this.setTargetDate();
        
        if (this.isRunning) {
            this.restart();
        }
    }
    
    getTimeLeft() {
        const now = new Date().getTime();
        const distance = this.targetDate.getTime() - now;
        
        if (distance < 0) return null;
        
        return this.calculateTimeLeft(distance);
    }
}

// CSS styles for countdown
const countdownCSS = `
<style>
.countdown-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    font-family: 'Open Sans', sans-serif;
}

.countdown-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 10px;
    border-radius: 8px;
    min-width: 60px;
}

.countdown-number {
    font-size: 2em;
    font-weight: bold;
    color: #007cba;
    line-height: 1;
}

.countdown-label {
    font-size: 0.8em;
    color: #ccc;
    text-transform: uppercase;
    margin-top: 5px;
}

.countdown-separator {
    font-size: 2em;
    font-weight: bold;
    color: #007cba;
}

.countdown-compact {
    font-size: 1.5em;
    font-weight: bold;
    color: #007cba;
}

.countdown-complete {
    font-size: 1.5em;
    font-weight: bold;
    color: #28a745;
    text-align: center;
    padding: 20px;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 8px;
}

@media (max-width: 768px) {
    .countdown-item {
        min-width: 50px;
        padding: 10px 8px;
    }
    
    .countdown-number {
        font-size: 1.5em;
    }
    
    .countdown-separator {
        font-size: 1.5em;
    }
}
</style>
`;

// Add CSS to head
if (!document.querySelector('#countdown-css')) {
    const style = document.createElement('style');
    style.id = 'countdown-css';
    style.innerHTML = countdownCSS.replace('<style>', '').replace('</style>', '');
    document.head.appendChild(style);
}

// Export for global use
window.CountdownTimer = CountdownTimer;
