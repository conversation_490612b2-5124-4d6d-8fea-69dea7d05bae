/**
 * Clean App.js - Replacement for obfuscated app.js
 * Contains essential functionality without obfuscation
 */

'use strict';

// Disable domain protection for local development
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('Local development mode - all protections disabled');
    window.DISABLE_DOMAIN_CHECK = true;
}

// Essential utility functions
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Place elements based on screen size
function placeTo(elementName, breakpoint) {
    if (!elementName || !breakpoint) return;
    
    function moveElement() {
        const container = document.querySelector(`[data-place-container="${elementName}"]`);
        const from = document.querySelector(`[data-place-from="${elementName}"]`);
        const to = document.querySelector(`[data-place-to="${elementName}"]`);
        
        if (window.innerWidth < breakpoint) {
            if (from && container) {
                from.appendChild(container);
            }
        } else {
            if (to && container) {
                to.appendChild(container);
            }
        }
    }
    
    window.addEventListener('resize', debounce(moveElement, 100));
    moveElement();
}

// Tab functionality
function gwTabHide(groups) {
    groups.forEach(group => {
        const elements = document.querySelectorAll(`[data-tab-group="${group}"]`);
        elements.forEach(el => el.style.display = 'none');
    });
}

function gwOpenTab(tabs) {
    tabs.forEach(tab => {
        const elements = document.querySelectorAll(`[data-tab="${tab}"]`);
        elements.forEach(el => el.style.display = 'block');
    });
}

function gwTabBtnsHandler(activeTabs, inactiveGroups) {
    // Remove active state from inactive groups
    inactiveGroups.forEach(group => {
        const buttons = document.querySelectorAll(`[data-open-tab-group*="${group}"]`);
        buttons.forEach(btn => btn.removeAttribute('data-open-tab-active'));
    });
    
    // Add active state to active tabs
    activeTabs.forEach(tab => {
        const buttons = document.querySelectorAll(`[data-open-tab="${tab}"]`);
        buttons.forEach(btn => btn.setAttribute('data-open-tab-active', 'true'));
    });
}

// Section handler for smooth scrolling
function SectionHandler(containerSelector, pointAttr, activeClass) {
    if (!containerSelector || !pointAttr || !activeClass) return;

    const container = document.querySelector(containerSelector);
    if (!container) {
        console.log(`Container not found: ${containerSelector}`);
        return;
    }

    const points = container.querySelectorAll(`[${pointAttr}]`);
    const sections = Array.from(document.querySelectorAll('[data-target-section]'));

    console.log(`SectionHandler initialized: ${points.length} points, ${sections.length} sections`);
    
    // Scroll handler
    const handleScroll = throttle(() => {
        const scrollTop = window.pageYOffset;
        const windowHeight = window.innerHeight;
        const triggerPoint = scrollTop + windowHeight / 2;
        
        let activeSection = null;
        
        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            const sectionTop = rect.top + scrollTop;
            const sectionBottom = sectionTop + rect.height;
            
            if (triggerPoint >= sectionTop && triggerPoint <= sectionBottom) {
                activeSection = section;
            }
        });
        
        if (activeSection) {
            const sectionName = activeSection.getAttribute('data-target-section');
            
            // Update points
            points.forEach(point => {
                point.classList.remove(activeClass);
                if (point.getAttribute(pointAttr) === sectionName) {
                    point.classList.add(activeClass);
                }
            });
            
            // Update sections
            sections.forEach(section => {
                section.classList.remove('section_active');
                if (section.getAttribute('data-target-section') === sectionName) {
                    section.classList.add('section_active');
                }
            });
        }
    }, 100);
    
    window.addEventListener('scroll', handleScroll);
    
    // Click handler for navigation
    points.forEach((point, index) => {
        console.log(`Adding click handler to point ${index}: ${point.getAttribute(pointAttr)}`);

        point.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Navigation point clicked:', point.getAttribute(pointAttr));

            const targetName = point.getAttribute(pointAttr);
            const targetSection = document.querySelector(`[data-target-section="${targetName}"]`);

            if (targetSection) {
                const offset = Number(targetSection.getAttribute('data-target-section-offset')) || 0;
                const targetTop = targetSection.getBoundingClientRect().top + window.pageYOffset + (offset * -16);

                console.log(`Scrolling to section: ${targetName}, top: ${targetTop}`);

                // Remove active class from all points
                points.forEach(p => p.classList.remove(activeClass));
                // Add active class to clicked point
                point.classList.add(activeClass);

                // Use GSAP if available, otherwise fallback to native scrollTo
                if (typeof gsap !== 'undefined') {
                    gsap.to(window, {
                        duration: 0.8,
                        scrollTo: { y: targetTop },
                        ease: "power2.out"
                    });
                } else {
                    window.scrollTo({
                        top: targetTop,
                        behavior: 'smooth'
                    });
                }
            } else {
                console.log(`Target section not found: ${targetName}`);
                console.log('Available sections:', sections.map(s => s.getAttribute('data-target-section')));
            }
        });
    });
}

// Animating numbers
function AnimatingNumbers(element, from, to, duration) {
    if (!element) return;
    
    const startTime = performance.now();
    
    function animate(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(from + (to - from) * progress);
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    }
    
    requestAnimationFrame(animate);
}

// Initialize global config if not exists
if (typeof window.__config === 'undefined') {
    window.__config = {
        posts: [],
        version: '2.0',
        language: 'en'
    };
}

// Mobile menu functionality
function initMobileMenu() {
    const burger = document.querySelector('[data-gw-burger]');
    const menuLinks = document.querySelectorAll('[data-menu-link]');
    const body = document.querySelector('body');

    if (burger) {
        burger.addEventListener('click', function() {
            body.classList.toggle('mob-menu-active');
        });
    }

    // Close menu when clicking on menu links
    menuLinks.forEach(link => {
        link.addEventListener('click', function() {
            body.classList.remove('mob-menu-active');
        });
    });

    // Handle scroll for header styling
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        const scrollTop = document.scrollingElement.scrollTop;
        if (scrollTop > 300) {
            body.classList.add('scrolled');
            body.classList.remove('top');
        } else {
            body.classList.remove('scrolled');
            body.classList.add('top');
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(function() {
            if (document.body.clientWidth >= 1300) {
                document.body.classList.remove('mob-menu-active');
            }
        }, 100);
    });
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tab functionality
    document.querySelectorAll('[data-open-tab]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const tabsToOpen = this.getAttribute('data-open-tab').split('|');
            const groupsToHide = this.getAttribute('data-open-tab-group').split('|');

            gwTabHide(groupsToHide);
            gwOpenTab(tabsToOpen);
            gwTabBtnsHandler(tabsToOpen, groupsToHide);

            document.dispatchEvent(new Event('gwtab'));
        });
    });

    // Initialize section handler for navigation points
    SectionHandler('[data-points]', 'data-point-name', 'point_active');

    // Additional event delegation for navigation points
    document.addEventListener('click', function(e) {
        const point = e.target.closest('[data-point-name]');
        if (point) {
            e.preventDefault();
            console.log('Point clicked via delegation:', point.getAttribute('data-point-name'));

            const targetName = point.getAttribute('data-point-name');
            const targetSection = document.querySelector(`[data-target-section="${targetName}"]`);

            if (targetSection) {
                const offset = Number(targetSection.getAttribute('data-target-section-offset')) || 0;
                const targetTop = targetSection.getBoundingClientRect().top + window.pageYOffset + (offset * -16);

                console.log(`Scrolling to: ${targetName}, top: ${targetTop}`);

                // Update active states
                document.querySelectorAll('[data-point-name]').forEach(p => p.classList.remove('point_active'));
                point.classList.add('point_active');

                // Scroll to target
                window.scrollTo({
                    top: targetTop,
                    behavior: 'smooth'
                });
            }
        }
    });

    // Initialize place elements
    placeTo('auth', 1300);
    placeTo('scl', 1300);

    // Initialize mobile menu
    initMobileMenu();

    // Initialize news functionality if loadNews function exists
    if (typeof window.loadNews === 'function') {
        window.loadNews();
    }

    console.log('Clean App.js initialized');
});

// News slider initialization function
function newsInit() {
    if (typeof __config === 'undefined' ||
        typeof __config.sliders === 'undefined' ||
        typeof __config.sliders.news === 'undefined' ||
        !__config.sliders.news.init) {
        console.log('News slider config not found, skipping initialization');
        return;
    }

    if (typeof Swiper === 'undefined') {
        console.log('Swiper not loaded, skipping news slider');
        return;
    }

    try {
        const newsSlider = new Swiper('[data-slider="news"]', {
            autoplay: __config.sliders.news.autoplay ? {
                disableOnInteraction: false,
                delay: __config.sliders.news.autoplayDelay || 10000
            } : false,
            pagination: {
                el: '[data-slider-dots="news"]',
                type: 'bullets',
                clickable: true,
                bulletClass: 'dot',
                bulletActiveClass: 'dot_active'
            },
            navigation: {
                nextEl: '[data-slider-next="news"]',
                prevEl: '[data-slider-prev="news"]'
            },
            loop: __config.sliders.news.loop || false,
            slidesPerView: 1,
            spaceBetween: 15,
            breakpoints: {
                0: {
                    slidesPerView: 1,
                    spaceBetween: 40
                },
                1050: {
                    slidesPerView: 2,
                    spaceBetween: 40
                }
            }
        });

        // Pause on hover if configured
        if (__config.sliders.news.pauseOnHover && newsSlider.autoplay) {
            const newsContainer = document.querySelector('[data-slider="news"]');
            if (newsContainer) {
                newsContainer.addEventListener('mouseenter', () => {
                    newsSlider.autoplay.stop();
                });
                newsContainer.addEventListener('mouseleave', () => {
                    newsSlider.autoplay.start();
                });
            }
        }

        console.log('News slider initialized');
    } catch (error) {
        console.error('Error initializing news slider:', error);
    }
}

// Make newsInit globally available
window.newsInit = newsInit;

// Animation system
let gwAnimeArray = [];

function gwAnime() {
    if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
        console.log('GSAP or ScrollTrigger not loaded, skipping animations');
        return;
    }

    // Clear existing animations
    gwAnimeArray.forEach(trigger => {
        if (trigger && typeof trigger.kill === 'function') {
            trigger.kill();
        }
    });
    gwAnimeArray = [];

    // Initialize animations for elements with data-gw-anime
    document.querySelectorAll('[data-gw-anime]').forEach(element => {
        const animationType = element.getAttribute('data-gw-anime');
        const delay = element.getAttribute('data-gw-anime-delay');
        const duration = element.getAttribute('data-gw-anime-duration');

        if (delay) {
            element.style.animationDelay = delay;
        }
        if (duration) {
            element.style.animationDuration = duration;
        }

        const trigger = ScrollTrigger.create({
            trigger: element,
            onEnter: () => element.classList.add(animationType),
            onEnterBack: () => element.classList.add(animationType),
            onLeave: () => element.classList.remove(animationType),
            onLeaveBack: () => element.classList.remove(animationType)
        });

        gwAnimeArray.push(trigger);
    });

    console.log('Animations initialized:', gwAnimeArray.length);
}

// Animation refresh functions
function gwAnimeRefresh() {
    gwAnimeArray.forEach(trigger => {
        if (trigger && typeof trigger.refresh === 'function') {
            trigger.refresh();
        }
    });
}

const gwAnimeRefreshThrottle = throttle(gwAnimeRefresh, 500);

// Auto-update animations
function gwAnimeAutoUpdate() {
    setTimeout(() => {
        gwAnimeRefreshThrottle();
        gwAnimeAutoUpdate();
    }, 5000);
}

// Make gwAnime globally available
window.gwAnime = gwAnime;
window.gwAnimeRefresh = gwAnimeRefresh;

// Preload functionality
function handlePreload() {
    const preloadElement = document.querySelector('.preload');
    if (!preloadElement) {
        // No preload element, trigger events immediately
        document.dispatchEvent(new Event('PreloadBefore'));
        document.querySelector('body').dispatchEvent(new Event('PreloadBefore'));
        document.dispatchEvent(new Event('PreloadEnd'));
        document.querySelector('body').dispatchEvent(new Event('PreloadEnd'));
        document.querySelector('body').classList.add('loaded');
        return;
    }

    // Dispatch preload start events
    document.dispatchEvent(new Event('PreloadBefore'));
    document.querySelector('body').dispatchEvent(new Event('PreloadBefore'));
    document.dispatchEvent(new Event('PreloadStart'));
    document.querySelector('body').dispatchEvent(new Event('PreloadStart'));

    // Wait for minimum time then remove preload
    const minTime = (window.__config && window.__config.preload && window.__config.preload.minTime) || 1;
    setTimeout(() => {
        preloadElement.classList.add('preload_fade');
        setTimeout(() => {
            preloadElement.remove();
            document.dispatchEvent(new Event('PreloadEnd'));
            document.querySelector('body').dispatchEvent(new Event('PreloadEnd'));
            document.querySelector('body').classList.add('loaded');
        }, 500);
    }, minTime * 1000);
}

// Initialize preload on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', handlePreload);
} else {
    handlePreload();
}

// Event listeners for animations and preload
document.addEventListener('PreloadEnd', gwAnime);
document.addEventListener('PreloadBefore', function() {
    window.scrollTo({ top: 0, behavior: 'auto' });
    SectionHandler('.section-slider-active', 'data-point-name', 'point_active');
});

window.addEventListener('resize', gwAnimeRefreshThrottle);
document.addEventListener('atab', gwAnimeRefreshThrottle);
document.addEventListener('gwtab', gwAnimeRefreshThrottle);
document.addEventListener('gw_sn_update', gwAnimeRefreshThrottle);

// Start auto-update
gwAnimeAutoUpdate();

// Preload functionality
function handlePreload() {
    const preloadElement = document.querySelector('.preload');
    if (!preloadElement) {
        // No preload element, trigger events immediately
        document.dispatchEvent(new Event('PreloadBefore'));
        document.querySelector('body').dispatchEvent(new Event('PreloadBefore'));
        document.dispatchEvent(new Event('PreloadEnd'));
        document.querySelector('body').dispatchEvent(new Event('PreloadEnd'));
        document.querySelector('body').classList.add('loaded');
        return;
    }

    // Dispatch preload start events
    document.dispatchEvent(new Event('PreloadBefore'));
    document.querySelector('body').dispatchEvent(new Event('PreloadBefore'));
    document.dispatchEvent(new Event('PreloadStart'));
    document.querySelector('body').dispatchEvent(new Event('PreloadStart'));

    // Wait for minimum time then remove preload
    const minTime = (window.__config && window.__config.preload && window.__config.preload.minTime) || 1;
    setTimeout(() => {
        preloadElement.classList.add('preload_fade');
        setTimeout(() => {
            preloadElement.remove();
            document.dispatchEvent(new Event('PreloadEnd'));
            document.querySelector('body').dispatchEvent(new Event('PreloadEnd'));
            document.querySelector('body').classList.add('loaded');
        }, 500);
    }, minTime * 1000);
}

// Initialize preload on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', handlePreload);
} else {
    handlePreload();
}

// Export functions for global use
window.placeTo = placeTo;
window.gwTabHide = gwTabHide;
window.gwOpenTab = gwOpenTab;
window.gwTabBtnsHandler = gwTabBtnsHandler;
window.SectionHandler = SectionHandler;
window.AnimatingNumbers = AnimatingNumbers;
