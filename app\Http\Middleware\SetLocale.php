<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $request->segment(1);

        // Check if the first segment is a valid locale
        if (in_array($locale, ['en', 'vn'])) {
            app()->setLocale($locale);
        } else {
            // Default to English if no locale specified
            app()->setLocale('en');
        }

        return $next($request);
    }
}
