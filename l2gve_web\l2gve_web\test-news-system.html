<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test News System - L2GVE</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>L2GVE News System Test</h1>
        
        <div class="test-section">
            <h2>🔗 Navigation Links</h2>
            <div class="links">
                <a href="en/index.html" class="btn">English Home</a>
                <a href="vn/index.html" class="btn">Vietnamese Home</a>
                <a href="en/all-news.html" class="btn">All News (EN)</a>
                <a href="vn/all-news.html" class="btn">All News (VN)</a>
                <a href="en/news-detail.html?id=1" class="btn">News Detail (EN)</a>
                <a href="vn/news-detail.html?id=1" class="btn">News Detail (VN)</a>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 API Tests</h2>
            <button class="btn" onclick="testAPI('all')">Test All News</button>
            <button class="btn" onclick="testAPI('news')">Test News Only</button>
            <button class="btn" onclick="testAPI('promotions')">Test Promotions Only</button>
            <button class="btn" onclick="testAPI('features')">Test Features Only</button>
            <button class="btn" onclick="testAPI('single', 1)">Test Single News (ID: 1)</button>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📊 System Status</h2>
            <div id="systemStatus">
                <p>Checking system status...</p>
            </div>
        </div>

        <div class="test-section">
            <h2>📝 Features Implemented</h2>
            <ul>
                <li>✅ Updated "All" button in index pages to link to all-news.html</li>
                <li>✅ Created all-news.html for both English and Vietnamese</li>
                <li>✅ News cards with category filtering</li>
                <li>✅ "Read More" buttons linking to news-detail.html</li>
                <li>✅ Enhanced news-detail.html with navigation buttons</li>
                <li>✅ Category-based filtering (All, News, Promotions, Features)</li>
                <li>✅ Responsive design for mobile devices</li>
                <li>✅ API support for category filtering</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🎨 Design Features</h2>
            <ul>
                <li>✅ Card-based layout for news items</li>
                <li>✅ Hover effects and animations</li>
                <li>✅ Category badges with different colors</li>
                <li>✅ Responsive grid layout</li>
                <li>✅ Loading states and error handling</li>
                <li>✅ Back navigation buttons</li>
                <li>✅ Consistent styling with main site</li>
            </ul>
        </div>
    </div>

    <script>
        // Test API functionality
        async function testAPI(type, id = null) {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';

            try {
                let url = 'api/news.php';
                
                if (type === 'single' && id) {
                    url += `?id=${id}`;
                } else if (type !== 'all') {
                    url += `?type=${type}&limit=10`;
                } else {
                    url += '?limit=10';
                }

                const response = await fetch(url);
                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                
                // Update status
                updateStatus('API Test', response.ok ? 'success' : 'error', 
                    response.ok ? `Successfully loaded ${type} news` : 'API request failed');
                
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                updateStatus('API Test', 'error', error.message);
            }
        }

        // Check system status
        async function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            const checks = [
                { name: 'API Endpoint', url: 'api/news.php?limit=1' },
                { name: 'English All News', url: 'en/all-news.html' },
                { name: 'Vietnamese All News', url: 'vn/all-news.html' },
                { name: 'English News Detail', url: 'en/news-detail.html' },
                { name: 'Vietnamese News Detail', url: 'vn/news-detail.html' }
            ];

            statusDiv.innerHTML = '<h3>System Status Checks:</h3>';

            for (const check of checks) {
                try {
                    const response = await fetch(check.url);
                    const status = response.ok ? 'success' : 'error';
                    const message = response.ok ? 'OK' : `HTTP ${response.status}`;
                    
                    statusDiv.innerHTML += `
                        <div style="margin: 10px 0;">
                            <strong>${check.name}:</strong> 
                            <span class="status ${status}">${message}</span>
                        </div>
                    `;
                } catch (error) {
                    statusDiv.innerHTML += `
                        <div style="margin: 10px 0;">
                            <strong>${check.name}:</strong> 
                            <span class="status error">Error: ${error.message}</span>
                        </div>
                    `;
                }
            }
        }

        function updateStatus(component, status, message) {
            console.log(`[${component}] ${status.toUpperCase()}: ${message}`);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
        });
    </script>
</body>
</html>
