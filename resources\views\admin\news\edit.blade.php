@extends('admin.layout')

@section('title', 'Edit News Article')

@section('styles')
<style>
.form-floating > .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
}

.form-floating > .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--accent-color);
    color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(0, 124, 186, 0.25);
}

.form-floating > label {
    color: rgba(255, 255, 255, 0.7);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: var(--accent-color);
}

.editor-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
}

.ql-toolbar {
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.08);
    border-radius: 8px 8px 0 0;
}

.ql-toolbar .ql-stroke {
    fill: none;
    stroke: #e0e0e0;
}

.ql-toolbar .ql-fill {
    fill: #e0e0e0;
    stroke: none;
}

.ql-toolbar .ql-picker-label {
    color: #e0e0e0;
}

.ql-toolbar button:hover,
.ql-toolbar button.ql-active {
    background: rgba(0, 124, 186, 0.2);
}

.ql-container {
    border: none;
    background: rgba(255, 255, 255, 0.05);
    color: #e0e0e0;
    font-size: 14px;
    border-radius: 0 0 8px 8px;
}

.ql-editor {
    min-height: 200px;
    color: #e0e0e0;
    background: rgba(255, 255, 255, 0.05);
    line-height: 1.6;
}

.ql-editor.ql-blank::before {
    color: rgba(224, 224, 224, 0.6);
    font-style: italic;
}

.ql-editor h1, .ql-editor h2, .ql-editor h3, .ql-editor h4, .ql-editor h5, .ql-editor h6 {
    color: #f5f5f5;
}

.ql-editor strong {
    color: #f5f5f5;
}

.ql-editor a {
    color: var(--accent-color);
}

.ql-picker-options {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ql-picker-item {
    color: #e0e0e0;
}

.ql-picker-item:hover {
    background: rgba(0, 124, 186, 0.2);
}

.image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    margin-top: 10px;
}

.form-check-input:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 124, 186, 0.25);
}

.article-meta {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
}
</style>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-edit me-2"></i>Edit Article: {{ Str::limit($news->title, 40) }}
    </h1>
    <div>
        <a href="{{ route('admin.news.show', $news->id) }}" class="btn btn-outline-info me-2">
            <i class="fas fa-eye me-2"></i>View
        </a>
        <a href="{{ route('admin.news.index') }}" class="btn btn-outline-light">
            <i class="fas fa-arrow-left me-2"></i>Back to News
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>Article Details
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.news.update', $news->id) }}" id="newsForm" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <!-- Title -->
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" placeholder="Article Title" 
                               value="{{ old('title', $news->title) }}" required>
                        <label for="title">Article Title</label>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Description/Excerpt -->
                    <div class="form-floating mb-3">
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" placeholder="Short description or excerpt" 
                                  style="height: 100px">{{ old('description', $news->description) }}</textarea>
                        <label for="description">Short Description/Excerpt</label>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Content Editor -->
                    <div class="mb-3">
                        <label class="form-label">Article Content</label>
                        <div class="editor-container">
                            <div id="editor"></div>
                            <textarea name="content" id="content" style="display: none;">{{ old('content', $news->content) }}</textarea>
                        </div>
                        @error('content')
                            <div class="text-danger small mt-1">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Image URL -->
                    <div class="form-floating mb-3">
                        <input type="url" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" placeholder="https://example.com/image.jpg" 
                               value="{{ old('image', $news->image) }}" onchange="previewImage(this)">
                        <label for="image">Featured Image URL</label>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <img id="imagePreview" class="image-preview" 
                             src="{{ $news->image }}" 
                             style="{{ $news->image ? 'display: block;' : 'display: none;' }}">
                    </div>

                    <div class="row">
                        <!-- Type -->
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="news" {{ old('type', $news->type) === 'news' ? 'selected' : '' }}>News</option>
                                    <option value="promotions" {{ old('type', $news->type) === 'promotions' ? 'selected' : '' }}>Promotions</option>
                                    <option value="features" {{ old('type', $news->type) === 'features' ? 'selected' : '' }}>Features</option>
                                </select>
                                <label for="type">Article Type</label>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                    <option value="active" {{ old('status', $news->status) === 'active' ? 'selected' : '' }}>Published</option>
                                    <option value="inactive" {{ old('status', $news->status) === 'inactive' ? 'selected' : '' }}>Draft</option>
                                </select>
                                <label for="status">Publication Status</label>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{{ route('admin.news.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <a href="{{ route('admin.news.show', $news->id) }}" class="btn btn-outline-info ms-2">
                                <i class="fas fa-eye me-2"></i>Preview
                            </a>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Article
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Article Meta Info -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Article Information
                </h5>
            </div>
            <div class="card-body">
                <div class="article-meta">
                    <div class="row g-3">
                        <div class="col-6">
                            <small class="text-muted">ID</small>
                            <div class="fw-bold">#{{ $news->id }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Type</small>
                            <div><span class="badge bg-info">{{ ucfirst($news->type) }}</span></div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Status</small>
                            <div><span class="badge bg-{{ $news->status === 'active' ? 'success' : 'warning' }}">
                                {{ $news->status === 'active' ? 'Published' : 'Draft' }}
                            </span></div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Created</small>
                            <div class="small">{{ $news->created_at->format('M d, Y') }}</div>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">Last Updated</small>
                            <div class="small">{{ $news->updated_at->diffForHumans() }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Publishing Tips -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Editing Tips
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <ul class="mb-0 small">
                        <li>Preview changes before saving</li>
                        <li>Update the excerpt if content changed significantly</li>
                        <li>Check image URLs are still valid</li>
                        <li>Consider SEO when editing titles</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let quill;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Quill editor
    quill = new Quill('#editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'align': [] }],
                ['link', 'image'],
                ['blockquote', 'code-block'],
                ['clean']
            ]
        },
        placeholder: 'Write your article content here...'
    });

    // Load existing content
    const existingContent = document.getElementById('content').value;
    if (existingContent) {
        quill.root.innerHTML = existingContent;
    }

    // Sync content with hidden textarea
    quill.on('text-change', function() {
        document.getElementById('content').value = quill.root.innerHTML;
    });
});

function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    if (input.value) {
        preview.src = input.value;
        preview.style.display = 'block';
        preview.onerror = function() {
            this.style.display = 'none';
        };
    } else {
        preview.style.display = 'none';
    }
}
</script>
@endsection
