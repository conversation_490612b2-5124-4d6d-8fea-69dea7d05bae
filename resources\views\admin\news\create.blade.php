@extends('admin.layout')

@section('title', 'Create News Article')

@section('styles')
<style>
.form-floating > .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
}

.form-floating > .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--accent-color);
    color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(0, 124, 186, 0.25);
}

.form-floating > label {
    color: rgba(255, 255, 255, 0.7);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: var(--accent-color);
}

.editor-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
}

.ql-toolbar {
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.08);
    border-radius: 8px 8px 0 0;
}

.ql-toolbar .ql-stroke {
    fill: none;
    stroke: #e0e0e0;
}

.ql-toolbar .ql-fill {
    fill: #e0e0e0;
    stroke: none;
}

.ql-toolbar .ql-picker-label {
    color: #e0e0e0;
}

.ql-toolbar button:hover,
.ql-toolbar button.ql-active {
    background: rgba(0, 124, 186, 0.2);
}

.ql-container {
    border: none;
    background: rgba(255, 255, 255, 0.05);
    color: #e0e0e0;
    font-size: 14px;
    border-radius: 0 0 8px 8px;
}

.ql-editor {
    min-height: 200px;
    color: #e0e0e0;
    background: rgba(255, 255, 255, 0.05);
    line-height: 1.6;
}

.ql-editor.ql-blank::before {
    color: rgba(224, 224, 224, 0.6);
    font-style: italic;
}

.ql-editor h1, .ql-editor h2, .ql-editor h3, .ql-editor h4, .ql-editor h5, .ql-editor h6 {
    color: #f5f5f5;
}

.ql-editor strong {
    color: #f5f5f5;
}

.ql-editor a {
    color: var(--accent-color);
}

.ql-picker-options {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ql-picker-item {
    color: #e0e0e0;
}

.ql-picker-item:hover {
    background: rgba(0, 124, 186, 0.2);
}

.image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    margin-top: 10px;
}

.form-check-input:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 124, 186, 0.25);
}
</style>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-plus me-2"></i>Create News Article
    </h1>
    <a href="{{ route('admin.news.index') }}" class="btn btn-outline-light">
        <i class="fas fa-arrow-left me-2"></i>Back to News
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>Article Details
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.news.store') }}" id="newsForm" enctype="multipart/form-data">
                    @csrf
                    
                    <!-- Title -->
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" placeholder="Article Title" value="{{ old('title') }}" required>
                        <label for="title">Article Title</label>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Description/Excerpt -->
                    <div class="form-floating mb-3">
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" placeholder="Short description or excerpt" 
                                  style="height: 100px">{{ old('description') }}</textarea>
                        <label for="description">Short Description/Excerpt</label>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Content Editor -->
                    <div class="mb-3">
                        <label class="form-label">Article Content</label>
                        <div class="editor-container">
                            <div id="editor"></div>
                            <textarea name="content" id="content" style="display: none;">{{ old('content') }}</textarea>
                        </div>
                        @error('content')
                            <div class="text-danger small mt-1">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Image Upload -->
                    <div class="mb-3">
                        <label class="form-label">Featured Image</label>

                        <!-- Upload Tabs -->
                        <div class="btn-group w-100 mb-3" role="group">
                            <input type="radio" class="btn-check" name="image_type" id="image_url_tab" value="url" checked>
                            <label class="btn btn-outline-light" for="image_url_tab">Image URL</label>

                            <input type="radio" class="btn-check" name="image_type" id="image_file_tab" value="file">
                            <label class="btn btn-outline-light" for="image_file_tab">Upload File</label>
                        </div>

                        <!-- URL Input -->
                        <div id="url_input" class="form-floating mb-3">
                            <input type="url" class="form-control @error('image_url') is-invalid @enderror"
                                   id="image_url" name="image_url" placeholder="https://example.com/image.jpg"
                                   value="{{ old('image_url') }}" onchange="previewImage(this)">
                            <label for="image_url">Image URL</label>
                            @error('image_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- File Upload -->
                        <div id="file_input" class="mb-3" style="display: none;">
                            <input type="file" class="form-control @error('image_file') is-invalid @enderror"
                                   id="image_file" name="image_file" accept="image/*" onchange="previewFile(this)">
                            @error('image_file')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Max file size: 2MB. Supported formats: JPEG, PNG, JPG, GIF. Images will be resized to 800x600px.</div>
                        </div>

                        <!-- Image Preview -->
                        <img id="imagePreview" class="image-preview" style="display: none;">
                    </div>

                    <div class="row">
                        <!-- Type -->
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="news" {{ old('type') === 'news' ? 'selected' : '' }}>News</option>
                                    <option value="promotions" {{ old('type') === 'promotions' ? 'selected' : '' }}>Promotions</option>
                                    <option value="features" {{ old('type') === 'features' ? 'selected' : '' }}>Features</option>
                                </select>
                                <label for="type">Article Type</label>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                    <option value="active" {{ old('status') === 'active' ? 'selected' : '' }}>Published</option>
                                    <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Draft</option>
                                </select>
                                <label for="status">Publication Status</label>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.news.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Article
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Publishing Tips
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Tips for Great Articles:</h6>
                    <ul class="mb-0 small">
                        <li>Use clear, descriptive titles</li>
                        <li>Write engaging excerpts (150-200 characters)</li>
                        <li>Include high-quality featured images</li>
                        <li>Format content with headings and lists</li>
                        <li>Preview before publishing</li>
                    </ul>
                </div>
                
                <div class="mt-3">
                    <h6>Article Types:</h6>
                    <ul class="small text-muted">
                        <li><strong>News:</strong> Server updates, announcements</li>
                        <li><strong>Promotions:</strong> Events, special offers</li>
                        <li><strong>Features:</strong> Game features, guides</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let quill;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Quill editor
    quill = new Quill('#editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'align': [] }],
                ['link', 'image'],
                ['blockquote', 'code-block'],
                ['clean']
            ]
        },
        placeholder: 'Write your article content here...'
    });

    // Sync content with hidden textarea
    quill.on('text-change', function() {
        document.getElementById('content').value = quill.root.innerHTML;
    });

    // Load existing content if any
    const existingContent = document.getElementById('content').value;
    if (existingContent) {
        quill.root.innerHTML = existingContent;
    }
});

// Handle image type tabs
document.addEventListener('DOMContentLoaded', function() {
    const urlTab = document.getElementById('image_url_tab');
    const fileTab = document.getElementById('image_file_tab');
    const urlInput = document.getElementById('url_input');
    const fileInput = document.getElementById('file_input');

    urlTab.addEventListener('change', function() {
        if (this.checked) {
            urlInput.style.display = 'block';
            fileInput.style.display = 'none';
            document.getElementById('image_file').value = '';
            document.getElementById('imagePreview').style.display = 'none';
        }
    });

    fileTab.addEventListener('change', function() {
        if (this.checked) {
            urlInput.style.display = 'none';
            fileInput.style.display = 'block';
            document.getElementById('image_url').value = '';
            document.getElementById('imagePreview').style.display = 'none';
        }
    });
});

function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    if (input.value) {
        preview.src = input.value;
        preview.style.display = 'block';
        preview.onerror = function() {
            this.style.display = 'none';
        };
    } else {
        preview.style.display = 'none';
    }
}

function previewFile(input) {
    const preview = document.getElementById('imagePreview');
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}
</script>
@endsection
