@extends('layouts.app')

@section('title', $news->title . ' - L2GVE')
@section('description', $news->excerpt ?: Str::limit(strip_tags($news->content), 160))

@section('content')
<section class="section" data-section="news-detail" id="news-detail">
    <div class="container">
        <div class="news-detail" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            
            <!-- Back Button -->
            <div class="back-wrap">
                <a href="{{ route('news.all', ['locale' => $locale]) }}" class="back">
                    <i class="gwi gwi_arrow-left"></i>
                    {{ __('messages.Back to News') }}
                </a>
            </div>
            
            <!-- News Article -->
            <article class="news-detail-article">
                <!-- Featured Image -->
                <div class="news-detail-image">
                    <img src="{{ $news->image ?: asset('template/site/l2gve/images/post/img-def-0.jpg') }}"
                         alt="{{ $news->title }}"
                         class="news-detail-img">
                    <div class="news-detail-overlay">
                        <div class="news-detail-meta">
                            <span class="news-detail-type">{{ ucfirst($news->type) }}</span>
                            <span class="news-detail-date">{{ $news->created_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="news-detail-content">
                    <header class="news-detail-header">
                        <h1 class="news-detail-title">{{ $news->title }}</h1>
                        @if($news->excerpt)
                            <p class="news-detail-excerpt">{{ $news->excerpt }}</p>
                        @endif
                    </header>

                    <div class="news-detail-body">
                        {!! nl2br(e($news->content)) !!}
                    </div>

                    <footer class="news-detail-footer">
                        <div class="news-detail-author">
                            <i class="gwi gwi_user"></i>
                            <span>{{ $news->author ?: 'L2GVE Team' }}</span>
                        </div>
                        <div class="news-detail-published">
                            <i class="gwi gwi_calendar"></i>
                            <span>{{ $news->formatted_date }}</span>
                        </div>
                    </footer>
                </div>
            </article>
            

        </div>
    </div>
</section>
@endsection

@section('styles')
<style>
.news-detail-article {
    max-width: 900px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.news-detail-image {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.news-detail-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-detail-article:hover .news-detail-img {
    transform: scale(1.05);
}

.news-detail-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 30px;
}

.news-detail-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.news-detail-type {
    background: #007cba;
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.news-detail-date {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
}

.news-detail-content {
    padding: 40px;
}

.news-detail-header {
    margin-bottom: 30px;
    text-align: center;
}

.news-detail-title {
    font-size: 32px;
    font-weight: 700;
    color: #fff;
    margin-bottom: 15px;
    line-height: 1.3;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.news-detail-excerpt {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-style: italic;
    margin: 0;
}

.news-detail-body {
    font-size: 16px;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30px;
}

.news-detail-body p {
    margin-bottom: 20px;
}

.news-detail-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.news-detail-author,
.news-detail-published {
    display: flex;
    align-items: center;
    gap: 8px;
}

.news-detail-author i,
.news-detail-published i {
    color: #007cba;
}

@media (max-width: 768px) {
    .news-detail-article {
        margin: 0 15px;
    }

    .news-detail-image {
        height: 250px;
    }

    .news-detail-content {
        padding: 25px;
    }

    .news-detail-title {
        font-size: 24px;
    }

    .news-detail-footer {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
</style>
@endsection

@section('scripts')
<script>
// News detail page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Add any news detail specific functionality here
});
</script>
@endsection
