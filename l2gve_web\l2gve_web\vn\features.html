<!DOCTYPE html>
<html lang="vi">

<head>
    <!-- Load bypass script FIRST -->
    <script src="../bypass-protection.js"></script>

    <!-- Preload critical resources -->
    <link rel="preload" href="../template/site/l2gve/js/clean-app.js" as="script">
    <link rel="preload" href="../template/site/l2gve/js/custom.js@v=1714752167" as="script">
    <link rel="preload" href="../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js" as="script">

    <meta charset="UTF-8">
    <title>Tính Năng Game - L2GVE</title>
    <meta name="Description" content="Khám phá tất cả các tính năng tuyệt vời và cập nhật trong máy chủ L2GVE Essence GvE x100">
    <meta name="Keywords" content="L2GVE tính năng, Lineage 2 tính năng, tính năng máy chủ">
       
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="theme-color" content="#0f1416">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="viewport" content="width=device-width">

    <link rel="shortcut icon" href="../template/site/l2gve/images/favicon/favicon.ico@v=1" type="image/x-icon">

    <!-- fonts -->
    <link rel="stylesheet" href="../template/site/l2gve/fonts/beaufortforlol/fonts.css" />
    <link rel="stylesheet" href="../template/site/l2gve/fonts/intro/stylesheet.css" />

    <!-- libs -->
    <link rel="stylesheet" href="../template/site/l2gve/libs/gwi/css/gwi.css" />
    <link rel="stylesheet" href="../template/site/l2gve/libs/fancybox/css/jquery.fancybox.min.css" />

    <!-- Main style -->
    <link rel="stylesheet" href="../template/site/l2gve/css/main.css@v=1719174774.css">
    <link rel="stylesheet" href="../template/site/l2gve/css/custom.css@v=1714752167.css">

    <style>
        .all-features {
            padding: 100px 0 50px;
            min-height: 100vh;
        }

        .all-features__header {
            text-align: center;
            margin-bottom: 50px;
        }

        .all-features__title {
            font-size: 48px;
            color: #fff;
            margin-bottom: 20px;
        }

        .all-features__subtitle {
            font-size: 18px;
            color: #ccc;
            margin-bottom: 40px;
        }

        .all-features__content {
            margin-bottom: 50px;
        }

        .all-features__grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 25px;
            margin-bottom: 40px;
        }

        .loading-spinner {
            text-align: center;
            padding: 50px 0;
            color: #fff;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left: 4px solid #007cba;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-features {
            text-align: center;
            padding: 50px 0;
            color: #ccc;
        }

        .no-features h3 {
            color: #fff;
            margin-bottom: 15px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 40px;
        }

        .pagination button {
            background: transparent;
            border: 2px solid #007cba;
            color: #007cba;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: #007cba;
            color: #fff;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination button.active {
            background: #007cba;
            color: #fff;
        }

        .pagination .page-info {
            color: #ccc;
            margin: 0 15px;
        }

        .feature-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #007cba;
            box-shadow: 0 10px 30px rgba(0, 124, 186, 0.3);
        }

        .feature-card__image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 15px 15px 0 0;
        }

        .feature-card__category {
            display: inline-block;
            background: #007cba;
            color: #fff;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            margin: 20px 20px 15px 20px;
            text-transform: uppercase;
            width: fit-content;
        }

        .feature-card__title {
            font-size: 18px;
            color: #fff;
            margin: 0 20px 15px 20px;
            font-weight: 600;
            line-height: 1.3;
        }

        .feature-card__description {
            color: #ccc;
            font-size: 14px;
            line-height: 1.6;
            margin: 0 20px 20px 20px;
            flex: 1;
        }

        .feature-card__footer {
            margin: 0 20px 20px 20px;
            margin-top: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .feature-card__date {
            color: #888;
            font-size: 12px;
        }

        .feature-card__read-more {
            background: linear-gradient(135deg, #007cba, #0056b3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .feature-card__read-more:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 124, 186, 0.4);
        }

        /* Language navigation enhancement */
        .lang__current .lang__name {
            font-weight: bold !important;
            color: #ffffff !important;
        }

        .lang__link .lang__name {
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .lang__link:hover .lang__name {
            opacity: 1;
        }

        @media (max-width: 1024px) {
            .all-features__grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .all-features__grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .all-features__title {
                font-size: 36px;
            }

            .pagination {
                flex-wrap: wrap;
                gap: 5px;
            }

            .pagination button {
                padding: 6px 12px;
                font-size: 14px;
            }

            .pagination .page-info {
                width: 100%;
                text-align: center;
                margin: 10px 0 0 0;
            }
        }
    </style>
</head>

<body class="body">
    <div class="bg">
        <img src="../template/site/l2gve/images/bg/bg-repeat.jpg" alt="bg" class="bg__img" aria-hidden="true" />
    </div>

    <!-- Navigation -->
    <div class="section compensate-for-scrollbar" data-section="navigation">
    <div class="container" data-container="navigation">
        <div class="navigation">
            <div class="navigation__box navigation__box_side_left">
                <a href="index" class="logo">
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img" style="width: 9rem" />
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img logo__img_hover" />
                </a>

            </div>

           <div class="navigation__menu menu" data-menu>
    <div class="menu__content">
        <ul class="menu__list">
            <li class="menu__el">
                <a href="index" class="menu__item" data-menu-close>
                    Trang chủ
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="all-news" class="menu__item" data-menu-close>
                    Tin tức
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="features" class="menu__item" data-menu-close>
                    Tính năng
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="download" class="menu__item" data-menu-close>
                    Tải game
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="#discord" class="menu__item" data-menu-close>
                    Discord
                </a>
            </li>
            <li class="menu__el menu__el_continer menu__el_desktop_none" data-place-from="scl">
                <div class="scl" data-place-container="scl">
                    <div class="scl__list">
                        <a href="index.html#Telegram" target="_blank" class="scl__item">
                            <i class="gwi gwi_c-telegram scl__ico-c-telegram"></i>
                        </a>
                        <a href="index.html#Discord" target="_blank" class="scl__item">
                            <i class="gwi gwi_discord scl__ico-discord"></i>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>            <div class="navigation__box navigation__box_side_right" data-place-to="auth">
                <div class="navigation__lang lang notranslate">
    <div class="lang__current">
        <div class="lang__name" data-current-lang>vn</div>
    </div>
    <div class="lang__list">
        <a href="features" class="lang__link lang__link_sub" data-google-lang="vi">
            <div class="lang__name">vn</div>
        </a>
        <a href="../en/features" class="lang__link lang__link_sub" data-google-lang="en">
            <div class="lang__name">en</div>
        </a>
    </div>
</div>


                
            </div>

            <div class="navigation__gw-burger gw-burger" data-gw-burger>
                <div class="gw-burger__box">
                    <div class="gw-burger__line gw-burger__line_pos_top"></div>
                    <div class="gw-burger__line gw-burger__line_pos_middle"></div>
                    <div class="gw-burger__line gw-burger__line_pos_bottom"></div>
                </div>
            </div>
        </div>
        <!-- END  navigation -->
    </div>
</div>
<!-- END  section -->

    <div class="page">
        <section class="section compensate-for-scrollbar" data-section="all-features">
            <div class="container">
                <div class="all-features">
                    <div class="navigation-height-compensate"></div>


                    <div class="all-features__header" style="margin-top: -50px;">
                        <h1 class="all-features__title">Tính Năng Game</h1>
                        <p class="all-features__subtitle">Khám phá tất cả các tính năng tuyệt vời và cập nhật trong L2GVE</p>
                    </div>

                    <div class="all-features__content">
                        <div id="loadingSpinner" class="loading-spinner">
                            <div class="spinner"></div>
                            <p>Đang tải tính năng...</p>
                        </div>

                        <div id="featuresContent" class="features-content" style="display: none;">
                            <!-- Features will be loaded here -->
                        </div>

                        <div id="noFeatures" class="no-features" style="display: none;">
                            <h3>Không có tính năng nào</h3>
                            <p>Hãy quay lại sau để xem các tính năng mới!</p>
                        </div>

                        <div id="pagination" class="pagination" style="display: none;">
                            <!-- Pagination will be generated here -->
                        </div>


                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Scripts -->
    <script src="../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js"></script>
    <script src="../template/site/l2gve/js/clean-app.js"></script>
    <script src="../template/site/l2gve/js/custom.js@v=1714752167"></script>

    <script>
        let allFeatures = [];
        let currentPage = 1;
        const itemsPerPage = 9; // 3 columns x 3 rows

        // Load all features from database
        async function loadAllFeatures() {
            try {
                showLoading();
                const response = await fetch('../api/news.php?limit=50&type=features');
                const data = await response.json();

                if (Array.isArray(data) && data.length > 0) {
                    allFeatures = data.filter(item => item.type === 'features');
                    displayFeatures(allFeatures);
                } else {
                    showNoFeatures();
                }
            } catch (error) {
                console.error('Error loading features:', error);
                // Fallback data
                allFeatures = [
                    {
                        id: 1,
                        title: "Hệ thống PvP nâng cao",
                        desc: "Trải nghiệm chiến đấu người với người căng thẳng với cơ chế PvP được cải tiến, lớp nhân vật cân bằng và phần thưởng hấp dẫn.",
                        img: "../template/site/l2gve/images/post/img-def-0.jpg",
                        type: "features",
                        date: "2024-08-01 12:00:00"
                    },
                    {
                        id: 2,
                        title: "Boss Raid tùy chỉnh",
                        desc: "Thách thức bản thân với các boss raid độc đáo có cơ chế đặc biệt và vật phẩm rơi độc quyền.",
                        img: "../template/site/l2gve/images/post/img-def-0.jpg",
                        type: "features",
                        date: "2024-07-28 15:30:00"
                    },
                    {
                        id: 3,
                        title: "Hệ thống chế tạo nâng cao",
                        desc: "Tạo ra trang bị mạnh mẽ với hệ thống chế tạo được nâng cấp có công thức và nguyên liệu mới.",
                        img: "../template/site/l2gve/images/post/img-def-0.jpg",
                        type: "features",
                        date: "2024-07-25 10:15:00"
                    }
                ];
                displayFeatures(allFeatures);
            }

            hideLoading();
        }

        // Display features with pagination
        function displayFeatures(featuresArray) {
            const featuresContent = document.getElementById('featuresContent');

            if (featuresArray.length === 0) {
                showNoFeatures();
                return;
            }

            // Calculate pagination
            const totalItems = featuresArray.length;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const currentFeatures = featuresArray.slice(startIndex, endIndex);

            // Generate HTML for current page features
            let html = '<div class="all-features__grid">';

            currentFeatures.forEach(feature => {
                const truncatedDesc = truncateText(feature.desc || feature.description || '', 120);
                const formattedDate = formatDate(feature.date || feature.created_at);
                const imageUrl = feature.img || feature.image || '../template/site/l2gve/images/post/img-def-0.jpg';

                html += `
                    <div class="feature-card">
                        <img src="${imageUrl}" alt="${feature.title}" class="feature-card__image"
                             onerror="this.src='../template/site/l2gve/images/post/img-def-0.jpg'">
                        <div class="feature-card__category">Tính năng</div>
                        <h3 class="feature-card__title">${feature.title}</h3>
                        <p class="feature-card__description">${truncatedDesc}</p>
                        <div class="feature-card__footer">
                            <span class="feature-card__date">${formattedDate}</span>
                            <a href="news-detail?id=${feature.id}" class="feature-card__read-more">Đọc thêm</a>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            featuresContent.innerHTML = html;

            // Show features content and generate pagination
            featuresContent.style.display = 'block';
            generatePagination(totalPages);
        }

        // Generate pagination controls
        function generatePagination(totalPages) {
            const paginationContainer = document.getElementById('pagination');

            if (totalPages <= 1) {
                paginationContainer.style.display = 'none';
                return;
            }

            let paginationHTML = '';

            // Previous button
            paginationHTML += `
                <button onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                    ← Trước
                </button>
            `;

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    paginationHTML += `<button class="active">${i}</button>`;
                } else {
                    paginationHTML += `<button onclick="changePage(${i})">${i}</button>`;
                }
            }

            // Next button
            paginationHTML += `
                <button onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                    Sau →
                </button>
            `;

            // Page info
            paginationHTML += `
                <div class="page-info">
                    Trang ${currentPage} / ${totalPages} (${allFeatures.length} tính năng)
                </div>
            `;

            paginationContainer.innerHTML = paginationHTML;
            paginationContainer.style.display = 'flex';
        }

        // Change page function
        function changePage(page) {
            const totalPages = Math.ceil(allFeatures.length / itemsPerPage);

            if (page < 1 || page > totalPages) return;

            currentPage = page;
            displayFeatures(allFeatures);

            // Scroll to top of features
            document.querySelector('.all-features__header').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Utility functions
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('vi-VN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function truncateText(text, maxLength) {
            if (text.length <= maxLength) return text;
            return text.substr(0, maxLength) + '...';
        }

        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('featuresContent').style.display = 'none';
            document.getElementById('noFeatures').style.display = 'none';
            document.getElementById('pagination').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        function showNoFeatures() {
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('featuresContent').style.display = 'none';
            document.getElementById('noFeatures').style.display = 'block';
            document.getElementById('pagination').style.display = 'none';
        }

        // Scroll to top function with slower animation
        function scrollToTop() {
            const scrollDuration = 1000; // 1 second
            const scrollStep = -window.scrollY / (scrollDuration / 15);

            function scrollAnimation() {
                if (window.scrollY !== 0) {
                    window.scrollBy(0, scrollStep);
                    setTimeout(scrollAnimation, 15);
                }
            }
            scrollAnimation();
        }

        // Load features when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadAllFeatures();
        });
    </script>

    <style>
        .gwi_up::before {
            content: "↑";
            margin-right: 10px;
        }
    </style>

</body>
</html>
