<?php
declare(strict_types=1);

// Setup and diagnostic page for L2GVE
$errors = [];
$warnings = [];
$success = [];

// Check PHP version
if (version_compare(PHP_VERSION, '8.0.0', '<')) {
    $errors[] = 'PHP 8.0+ required. Current version: ' . PHP_VERSION;
} else {
    $success[] = 'PHP version: ' . PHP_VERSION . ' ✅';
}

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $errors[] = "Required extension missing: $ext";
    } else {
        $success[] = "Extension loaded: $ext ✅";
    }
}

// Check OPcache
if (!extension_loaded('opcache')) {
    $warnings[] = 'OPcache extension not loaded - performance will be reduced';
} else {
    $success[] = 'OPcache extension loaded ✅';
    
    if (function_exists('opcache_get_status') && opcache_get_status()) {
        $success[] = 'OPcache is active ✅';
        
        $jitBuffer = ini_get('opcache.jit_buffer_size');
        if ($jitBuffer && $jitBuffer !== '0') {
            $success[] = "JIT enabled with buffer: $jitBuffer ✅";
        } else {
            $warnings[] = 'JIT not enabled - add opcache.jit_buffer_size=512M to php.ini';
        }
    } else {
        $warnings[] = 'OPcache loaded but not active';
    }
}

// Check directories
$requiredDirs = ['cache', 'config', 'template', 'admin'];
foreach ($requiredDirs as $dir) {
    if (!is_dir(__DIR__ . '/' . $dir)) {
        $errors[] = "Required directory missing: $dir";
    } else {
        $success[] = "Directory exists: $dir ✅";
    }
}

// Check cache directory writable
$cacheDir = __DIR__ . '/cache';
if (!is_writable($cacheDir)) {
    $errors[] = 'Cache directory is not writable';
} else {
    $success[] = 'Cache directory is writable ✅';
}

// Create cache subdirectories
$cacheSubdirs = ['pages', 'assets', 'api', 'templates', 'bundles'];
foreach ($cacheSubdirs as $subdir) {
    $path = $cacheDir . '/' . $subdir;
    if (!is_dir($path)) {
        if (mkdir($path, 0755, true)) {
            $success[] = "Created cache directory: $subdir ✅";
        } else {
            $errors[] = "Failed to create cache directory: $subdir";
        }
    }
}

// Test file operations
$testFile = $cacheDir . '/test.txt';
if (file_put_contents($testFile, 'test') !== false) {
    $success[] = 'File write test passed ✅';
    unlink($testFile);
} else {
    $errors[] = 'File write test failed';
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>L2GVE Setup & Diagnostics</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            backdrop-filter: blur(10px);
            border-radius: 20px; 
            padding: 40px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        h1 { text-align: center; margin-bottom: 30px; font-size: 2.5rem; }
        .section { margin: 30px 0; }
        .section h2 { margin-bottom: 15px; font-size: 1.5rem; }
        .message { 
            padding: 12px 20px; 
            margin: 8px 0; 
            border-radius: 8px; 
            border-left: 4px solid;
        }
        .success { 
            background: rgba(46, 204, 113, 0.2); 
            border-color: #2ecc71; 
            color: #2ecc71;
        }
        .warning { 
            background: rgba(241, 196, 15, 0.2); 
            border-color: #f1c40f; 
            color: #f1c40f;
        }
        .error { 
            background: rgba(231, 76, 60, 0.2); 
            border-color: #e74c3c; 
            color: #e74c3c;
        }
        .status-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .status-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .status-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: rgba(52, 152, 219, 0.8); 
            color: white; 
            text-decoration: none; 
            border-radius: 8px; 
            margin: 10px 5px; 
            transition: all 0.3s ease;
        }
        .btn:hover { 
            background: rgba(52, 152, 219, 1); 
            transform: translateY(-2px);
        }
        .btn-success { background: rgba(46, 204, 113, 0.8); }
        .btn-success:hover { background: rgba(46, 204, 113, 1); }
        .next-steps {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 L2GVE Setup & Diagnostics</h1>
        
        <div class="status-summary">
            <div class="status-card">
                <div class="status-number" style="color: #2ecc71;"><?= count($success) ?></div>
                <div>Checks Passed</div>
            </div>
            <div class="status-card">
                <div class="status-number" style="color: #f1c40f;"><?= count($warnings) ?></div>
                <div>Warnings</div>
            </div>
            <div class="status-card">
                <div class="status-number" style="color: #e74c3c;"><?= count($errors) ?></div>
                <div>Errors</div>
            </div>
        </div>

        <?php if (!empty($success)): ?>
        <div class="section">
            <h2>✅ Success</h2>
            <?php foreach ($success as $msg): ?>
                <div class="message success"><?= htmlspecialchars($msg) ?></div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (!empty($warnings)): ?>
        <div class="section">
            <h2>⚠️ Warnings</h2>
            <?php foreach ($warnings as $msg): ?>
                <div class="message warning"><?= htmlspecialchars($msg) ?></div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
        <div class="section">
            <h2>❌ Errors</h2>
            <?php foreach ($errors as $msg): ?>
                <div class="message error"><?= htmlspecialchars($msg) ?></div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <div class="next-steps">
            <h2>🎯 Next Steps</h2>
            
            <?php if (empty($errors)): ?>
                <p>✅ Your system is ready! You can now:</p>
                <div style="text-align: center; margin-top: 20px;">
                    <a href="test.php" class="btn btn-success">🧪 Run Performance Test</a>
                    <a href="en/" class="btn">🇺🇸 View English Site</a>
                    <a href="vn/" class="btn">🇻🇳 View Vietnamese Site</a>
                    <a href="admin/" class="btn">⚙️ Admin Panel</a>
                </div>
            <?php else: ?>
                <p>❌ Please fix the errors above before proceeding.</p>
                <div style="margin-top: 15px;">
                    <h3>Common Solutions:</h3>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>Update PHP to version 8.0 or higher</li>
                        <li>Enable required PHP extensions in php.ini</li>
                        <li>Set proper directory permissions (755 for directories, 644 for files)</li>
                        <li>Enable OPcache and JIT in php.ini</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>

        <div style="text-align: center; margin-top: 30px; opacity: 0.8;">
            <p>🔄 <a href="setup.php" style="color: #3498db;">Refresh Diagnostics</a></p>
            <p style="margin-top: 10px; font-size: 0.9rem;">
                Server: <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?><br>
                Document Root: <?= $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' ?><br>
                Current Path: <?= __DIR__ ?>
            </p>
        </div>
    </div>
</body>
</html>
