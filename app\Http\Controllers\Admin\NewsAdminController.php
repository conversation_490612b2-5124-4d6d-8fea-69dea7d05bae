<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class NewsAdminController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = News::query();

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('language')) {
            $query->where('language', $request->language);
        }

        if ($request->filled('status')) {
            if ($request->status === 'published') {
                $query->where('status', 'active');
            } elseif ($request->status === 'draft') {
                $query->where('status', 'inactive');
            }
        }

        $news = $query->orderBy('created_at', 'desc')->paginate(15);

        // Preserve query parameters in pagination links
        $news->appends($request->query());

        return view('admin.news.index', compact('news'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.news.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'description' => 'nullable|string|max:500',
            'image_url' => 'nullable|url',
            'image_file' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'type' => 'required|in:news,promotions,features',
            'status' => 'required|in:active,inactive'
        ]);

        // Handle image upload
        $imagePath = $this->handleImageUpload($request);

        $validated['image'] = $imagePath;
        unset($validated['image_url'], $validated['image_file']);

        News::create($validated);

        return redirect()->route('admin.news.index')->with('success', 'News created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $news = News::findOrFail($id);
        return view('admin.news.show', compact('news'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $news = News::findOrFail($id);
        return view('admin.news.edit', compact('news'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $news = News::findOrFail($id);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'description' => 'nullable|string|max:500',
            'image_url' => 'nullable|url',
            'image_file' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'type' => 'required|in:news,promotions,features',
            'status' => 'required|in:active,inactive'
        ]);

        // Handle image upload
        $imagePath = $this->handleImageUpload($request, $news->image);

        if ($imagePath) {
            $validated['image'] = $imagePath;
        }
        unset($validated['image_url'], $validated['image_file']);

        $news->update($validated);

        return redirect()->route('admin.news.index')->with('success', 'News updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $news = News::findOrFail($id);
        $news->delete();

        return redirect()->route('admin.news.index')->with('success', 'News deleted successfully!');
    }

    /**
     * Handle image upload and processing
     */
    private function handleImageUpload(Request $request, $existingImage = null)
    {
        $imagePath = $existingImage;

        // Handle file upload
        if ($request->hasFile('image_file')) {
            // Delete existing image if it's a local file
            if ($existingImage && str_starts_with($existingImage, '/storage/')) {
                $oldPath = str_replace('/storage/', '', $existingImage);
                Storage::disk('public')->delete($oldPath);
            }

            $file = $request->file('image_file');
            $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            $path = 'images/' . $filename;

            // Store and resize image
            $file->storeAs('public/' . dirname($path), basename($path));

            // Resize image to standard dimensions (800x600)
            $fullPath = storage_path('app/public/' . $path);
            if (extension_loaded('gd') || extension_loaded('imagick')) {
                try {
                    $img = Image::make($fullPath);
                    $img->fit(800, 600, function ($constraint) {
                        $constraint->upsize();
                    });
                    $img->save($fullPath, 85); // 85% quality
                } catch (\Exception $e) {
                    // If image processing fails, keep original
                }
            }

            $imagePath = '/storage/' . $path;
        }
        // Handle URL input
        elseif ($request->filled('image_url')) {
            $imagePath = $request->image_url;
        }

        return $imagePath;
    }
}
