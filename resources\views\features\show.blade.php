@extends('layouts.app')

@section('title', $feature->title . ' - L2GVE')
@section('description', $feature->excerpt ?: Str::limit(strip_tags($feature->content), 160))

@section('content')
<section class="section" data-section="feature-detail" id="feature-detail">
    <div class="container">
        <div class="feature-detail" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            
            <!-- Back Button -->
            <div class="back-wrap">
                <a href="{{ route('features.all', ['locale' => $locale]) }}" class="back">
                    <i class="gwi gwi_arrow-left"></i>
                    {{ __('messages.Back to Features') }}
                </a>
            </div>
            
            <!-- Feature Article -->
            <article class="feature-detail-article">
                <!-- Featured Image -->
                <div class="feature-detail-image">
                    <img src="{{ $feature->image ?: asset('template/site/l2gve/images/post/img-def-0.jpg') }}"
                         alt="{{ $feature->title }}"
                         class="feature-detail-img">
                    <div class="feature-detail-overlay">
                        <div class="feature-detail-meta">
                            <span class="feature-detail-type">{{ ucfirst($feature->type) }}</span>
                            <span class="feature-detail-date">{{ $feature->created_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="feature-detail-content">
                    <header class="feature-detail-header">
                        <h1 class="feature-detail-title">{{ $feature->title }}</h1>
                        @if($feature->excerpt)
                            <p class="feature-detail-excerpt">{{ $feature->excerpt }}</p>
                        @endif
                    </header>

                    <div class="feature-detail-body">
                        {!! nl2br(e($feature->content)) !!}
                    </div>

                    <footer class="feature-detail-footer">
                        <div class="feature-detail-author">
                            <i class="gwi gwi_user"></i>
                            <span>{{ $feature->author ?: 'L2GVE Team' }}</span>
                        </div>
                        <div class="feature-detail-published">
                            <i class="gwi gwi_calendar"></i>
                            <span>{{ $feature->formatted_date }}</span>
                        </div>
                    </footer>
                </div>
            </article>
            

        </div>
    </div>
</section>
@endsection

@section('styles')
<style>
.feature-detail-article {
    max-width: 900px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.feature-detail-image {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.feature-detail-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.feature-detail-article:hover .feature-detail-img {
    transform: scale(1.05);
}

.feature-detail-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 30px;
}

.feature-detail-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.feature-detail-type {
    background: #ff6b35;
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.feature-detail-date {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
}

.feature-detail-content {
    padding: 40px;
}

.feature-detail-header {
    margin-bottom: 30px;
    text-align: center;
}

.feature-detail-title {
    font-size: 32px;
    font-weight: 700;
    color: #fff;
    margin-bottom: 15px;
    line-height: 1.3;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.feature-detail-excerpt {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-style: italic;
    margin: 0;
}

.feature-detail-body {
    font-size: 16px;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30px;
}

.feature-detail-body p {
    margin-bottom: 20px;
}

.feature-detail-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.feature-detail-author,
.feature-detail-published {
    display: flex;
    align-items: center;
    gap: 8px;
}

.feature-detail-author i,
.feature-detail-published i {
    color: #ff6b35;
}

@media (max-width: 768px) {
    .feature-detail-article {
        margin: 0 15px;
    }

    .feature-detail-image {
        height: 250px;
    }

    .feature-detail-content {
        padding: 25px;
    }

    .feature-detail-title {
        font-size: 24px;
    }

    .feature-detail-footer {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
</style>
@endsection

@section('scripts')
<script>
// Feature detail page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Add any feature detail specific functionality here
});
</script>
@endsection
