@extends('admin.layout')

@section('title', 'Dashboard')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
    </h1>
    <div class="text-muted">
        <i class="fas fa-clock me-1"></i>{{ now()->format('M d, Y - H:i') }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ $stats['total_news'] }}</div>
            <div class="stats-label">Total News</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="stats-number text-success">{{ $stats['published_news'] }}</div>
            <div class="stats-label">Published</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="stats-number text-warning">{{ $stats['draft_news'] }}</div>
            <div class="stats-label">Drafts</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="stats-number text-info">{{ $stats['recent_news']->count() }}</div>
            <div class="stats-label">Recent</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create New Article
                    </a>
                    <a href="{{ route('admin.news.index') }}" class="btn btn-outline-light">
                        <i class="fas fa-list me-2"></i>Manage All News
                    </a>
                    <a href="{{ route('home', ['locale' => 'en']) }}" target="_blank" class="btn btn-outline-info">
                        <i class="fas fa-external-link-alt me-2"></i>View Website
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent News -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Recent News
                </h5>
                <a href="{{ route('admin.news.index') }}" class="btn btn-sm btn-outline-light">View All</a>
            </div>
            <div class="card-body">
                @if($stats['recent_news']->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($stats['recent_news'] as $news)
                            <div class="list-group-item bg-transparent border-secondary">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 text-white">{{ Str::limit($news->title, 40) }}</h6>
                                        <p class="mb-1 text-muted small">{{ Str::limit($news->excerpt, 60) }}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>{{ $news->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                    <div class="ms-2">
                                        <span class="badge bg-{{ $news->published ? 'success' : 'warning' }}">
                                            {{ $news->published ? 'Published' : 'Draft' }}
                                        </span>
                                        <br>
                                        <span class="badge bg-info mt-1">{{ ucfirst($news->type) }}</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-newspaper fa-3x mb-3 opacity-50"></i>
                        <p>No news articles yet.</p>
                        <a href="{{ route('admin.news.create') }}" class="btn btn-primary btn-sm">
                            Create First Article
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- System Info -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <strong>Laravel Version:</strong><br>
                        <span class="text-muted">{{ app()->version() }}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>PHP Version:</strong><br>
                        <span class="text-muted">{{ PHP_VERSION }}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>Environment:</strong><br>
                        <span class="badge bg-{{ app()->environment() === 'production' ? 'success' : 'warning' }}">
                            {{ ucfirst(app()->environment()) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh stats every 30 seconds
    setInterval(function() {
        // You can implement AJAX refresh here if needed
    }, 30000);
});
</script>
@endsection
