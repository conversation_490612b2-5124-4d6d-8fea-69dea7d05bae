<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Load bypass script FIRST -->
    <script src="../bypass-protection.js"></script>

    <!-- Preload critical resources -->
    <link rel="preload" href="../template/site/l2gve/js/clean-app.js" as="script">
    <link rel="preload" href="../template/site/l2gve/js/custom.js@v=1714752167" as="script">
    <link rel="preload" href="../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js" as="script">

    <!-- DNS prefetch for external domains -->
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    <link rel="dns-prefetch" href="//mc.yandex.ru">
    <link rel="dns-prefetch" href="//ajax.googleapis.com">

    <meta charset="UTF-8">
    <!-- Rest of head content -->

    <!-- Defer analytics scripts to improve page load -->
    <script>
        // Defer Google Analytics
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'UA-167385217-1', { 'anonymize_ip': true });

        // Load GA script after page load
        window.addEventListener('load', function() {
            const gaScript = document.createElement('script');
            gaScript.async = true;
            gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=UA-167385217-1';
            document.head.appendChild(gaScript);
        });
    </script>

    <script>
        // Defer GTM
        window.addEventListener('load', function() {
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-T99S9NM7');
        });
    </script>

    <script>
        // Defer Yandex Metrika
        window.addEventListener('load', function() {
            (function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
                m[i].l=1*new Date();k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
                (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");
            ym(97762192, "init", {
                clickmap:true,
                trackLinks:true,
                accurateTrackBounce:true,
                webvisor:true
            });
        });
    </script>
    <noscript><div><img src="https://mc.yandex.ru/watch/97762192" style="position:absolute; left:-9999px;" alt="" /></div></noscript>        <title>L2GVE - Máy chủ Essence GvE x100</title>
    <meta name="Description" content="Dive into the world of Lineage II with the L2GVE Essence GvE x100 server">
    <meta name="Keywords" content="Essence GvE x100">
       
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="theme-color" content="#0f1416">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="viewport" content="width=device-width">
    <!-- <meta name="author" content="Front-End by Vitalii P. > Get-Web.site"> -->

    <link rel="shortcut icon" href="../template/site/l2gve/images/favicon/favicon.ico@v=1" type="image/x-icon">
<link rel="icon" sizes="16x16" href="../template/site/l2gve/images/favicon/favicon-16x16.png@v=1" type="image/png">
<link rel="icon" sizes="32x32" href="../template/site/l2gve/images/favicon/favicon-32x32.png@v=1" type="image/png">
<link rel="apple-touch-icon-precomposed" href="../template/site/l2gve/images/favicon/apple-touch-icon-precomposed.png@v=1">
<link rel="apple-touch-icon" href="../template/site/l2gve/images/favicon/apple-touch-icon.png@v=1">
<link rel="apple-touch-icon" sizes="57x57" href="../template/site/l2gve/images/favicon/apple-touch-icon-57x57.png@v=1">
<link rel="apple-touch-icon" sizes="60x60" href="../template/site/l2gve/images/favicon/apple-touch-icon-60x60.png@v=1">
<link rel="apple-touch-icon" sizes="72x72" href="../template/site/l2gve/images/favicon/apple-touch-icon-72x72.png@v=1">
<link rel="apple-touch-icon" sizes="76x76" href="../template/site/l2gve/images/favicon/apple-touch-icon-76x76.png@v=1">
<link rel="apple-touch-icon" sizes="114x114" href="../template/site/l2gve/images/favicon/apple-touch-icon-114x114.png@v=1">
<link rel="apple-touch-icon" sizes="120x120" href="../template/site/l2gve/images/favicon/apple-touch-icon-120x120.png@v=1">
<link rel="apple-touch-icon" sizes="144x144" href="../template/site/l2gve/images/favicon/apple-touch-icon-144x144.png@v=1">
<link rel="apple-touch-icon" sizes="152x152" href="../template/site/l2gve/images/favicon/apple-touch-icon-152x152.png@v=1">
<link rel="apple-touch-icon" sizes="167x167" href="../template/site/l2gve/images/favicon/apple-touch-icon-167x167.png@v=1">
<link rel="apple-touch-icon" sizes="180x180" href="../template/site/l2gve/images/favicon/apple-touch-icon-180x180.png@v=1">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://l2gve.com/">
    <meta property="og:title" content="L2GVE - Máy chủ Essence GvE x100">
    <meta property="og:image" content="/template/site/l2gve/images/sclbnr-en.jpg">
    <meta property="og:description" content="Dive into the world of Lineage II with the L2GVE Essence GvE x100 server">
    <meta property="og:site_name" content="L2GVE Essence GvE x100">
    <meta property="og:locale" content="en_US">
    <!-- Next tags are optional but recommended -->
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <style>
    .preload {
        background-color: #0f1416;
        min-width: 320px;
        position: fixed;
        z-index: 500;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        box-sizing: border-box;
    }

    /* Back to Top Button Styles */
    .back-to-top {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%) translateY(100px);
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
        border: 2px solid #4a5568;
        border-radius: 50%;
        color: #e2e8f0;
        cursor: pointer;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
    }

    .back-to-top:hover {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        border-color: #718096;
        color: #ffffff;
        transform: translateX(-50%) translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
    }

    .back-to-top.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
    }

    .back-to-top svg {
        transition: transform 0.2s ease;
    }

    .back-to-top:hover svg {
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .back-to-top {
            width: 45px;
            height: 45px;
            bottom: 20px;
        }
    }
</style>

<!-- fonts -->
<link rel="stylesheet" href="../template/site/l2gve/fonts/beaufortforlol/fonts.css" />
<link rel="stylesheet" href="../template/site/l2gve/fonts/intro/stylesheet.css" />

<!-- libs -->
<link rel="stylesheet" href="../template/site/l2gve/libs/gwi/css/gwi.css" />
<link rel="stylesheet" href="../template/site/l2gve/libs/fancybox/css/jquery.fancybox.min.css" />
<link rel="stylesheet" href="../template/site/l2gve/libs/swiper/css/swiper.min.css" />

<!-- Main style -->
<link rel="stylesheet" href="../template/site/l2gve/css/main.css@v=1719174774.css">
<link rel="stylesheet" href="../template/site/l2gve/css/custom.css@v=1714752167.css">

<style>
    /* Language navigation enhancement */
    .lang__current .lang__name {
        font-weight: bold !important;
        color: #ffffff !important;
    }

    .lang__link .lang__name {
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .lang__link:hover .lang__name {
        opacity: 1;
    }

    /* News item styles */
    .news__item {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        overflow: hidden;
        transition: transform 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .news__item:hover {
        transform: translateY(-5px);
    }

    .news__img {
        width: 100%;
        height: 200px;
        overflow: hidden;
    }

    .news__img img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .news__content {
        padding: 20px;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .news__meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-size: 12px;
        opacity: 0.8;
        gap: 10px;
    }

    .news__meta .news__type {
        flex: none !important;
        flex-shrink: 0 !important;
    }

    .news__type {
        background: #007cba !important;
        color: white !important;
        padding: 4px 12px !important;
        border-radius: 15px !important;
        text-transform: uppercase !important;
        font-size: 11px !important;
        font-weight: 600 !important;
        white-space: nowrap !important;
        display: inline-block !important;
        min-width: fit-content !important;
        width: auto !important;
        height: auto !important;
        max-width: none !important;
        flex: none !important;
        flex-shrink: 0 !important;
        flex-grow: 0 !important;
        flex-basis: auto !important;
        line-height: 1.2 !important;
    }

    .news__title {
        font-size: 18px;
        margin-bottom: 10px;
        color: #fff;
        line-height: 1.3;
    }

    .news__desc {
        color: #ccc;
        margin-bottom: 15px;
        line-height: 1.5;
        flex: 1;
    }

    .news__link {
        display: inline-block;
        text-decoration: none;
        margin-top: auto;
    }

    /* Fix scroll issues */
    html, body {
        scroll-behavior: smooth !important;
        overflow-x: hidden !important;
        overflow-y: auto !important;
    }

    /* Ensure no elements block scrolling */
    * {
        scroll-behavior: inherit !important;
    }

    /* Fix any potential anchor blocking */
    .section {
        scroll-margin-top: 0 !important;
    }
</style>
        <script>
        const __config = {
            gFonts: {
                fonts: ["Open Sans:400,500,600,700:latin,vietnamese"],
                delay: 500,
            },
            preload: {
                /* Minimum display time in seconds */
                /* Минимальное время показа в секундах */
                minTime: 3,

                /* Maximum display time in seconds */
                /* Максимальное время показа в секундах */
                maxTime: 10,

                /* Use the load event.
                If the event occurs earlier than min Time, the preloader will be hidden after the expiration of minTime.
                Otherwise, the preloader will be hidden if the event occurs later than minTime, but before maxTime */
                /* Использовать событие load.
                Если событие наступит раньше чем minTime, то прелоадер скроется по истечению minTime.
                Иначе прелоадер скроется если событие наступит позже minTime, но раньше maxTime */
                withOnload: true,

                /* Condition check update rate in seconds */
                /* Скорость обновления проверки условий в секундах */
                timeInterval: 0.5,
            },
            sectionSwitcher: {
                // Включить/отключить переключение секций колесиком
                init: true,
                // Скорость переключения между секциями
                // Switching speed between sections
                speed: 0.4,
                easeType: "power3.out",
            },
            sliders: {
                news: {
                    init: true,
                    loop: false,
                    autoplay: false,
                    autoplayDelay: 10000,
                },
            },
            gwtraslate: {
                /* Original language */
                lang: "vn",

                /* The language we translate into on the first visit*/
                /* Язык, на который переводим при первом посещении */
                langFirstVisit: 'vn',

                /* Если скрипт не работает или работает неправильно, раскомментируйте и укажите основной домен в свойстве domain */
                /* If the script does not work or does not work correctly, uncomment and specify the main domain in the domain property */
                /* domain: "Get-Web.Site" */
            },
            scl: [
            /* {
                    type: "c-telegram",
                    link: "#",
                    target: "_blank",
                } */
            ],
            eDate: {
                initial: false,
            },
        };
    </script>
</head>

<body class="body body_home">

    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T99S9NM7" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- <div class="preload">
    <div class="preload__progress" data-preload-progress></div>
    <img src="../template/site/l2gve/images/preload/preload.png" alt="loading.." class="preload__logo" style="width: 9rem" />
    <div class="preload__items">
        <img src="../template/site/l2gve/images/preload/item.png" alt="★" style="height: 1.4rem" />
        <img src="../template/site/l2gve/images/preload/item.png" alt="★" style="height: 1.4rem" />
        <img src="../template/site/l2gve/images/preload/item.png" alt="★" style="height: 1.4rem" />
    </div>
    </div> -->
    <div class="section compensate-for-scrollbar" data-section="navigation">
    <div class="container" data-container="navigation">
        <div class="navigation">
            <div class="navigation__box navigation__box_side_left">
                <a href="index" class="logo">
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img" style="width: 9rem" />
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img logo__img_hover" />
                </a>

            </div>

           <div class="navigation__menu menu" data-menu>
    <div class="menu__content">
        <ul class="menu__list">
            <li class="menu__el">
                <a href="index" class="menu__item" data-menu-close>
                    Home
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="all-news" class="menu__item" data-menu-close>
                    News
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="features" class="menu__item" data-menu-close>
                    Tính Năng
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="download" class="menu__item" data-menu-close>
                    Downloads
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="#discord" class="menu__item" data-menu-close>
                    Discord
                </a>
            </li>
            <li class="menu__el menu__el_continer menu__el_desktop_none" data-place-from="scl">
                <div class="scl" data-place-container="scl">
                    <div class="scl__list">
                        <a href="index.html#Telegram" target="_blank" class="scl__item">
                            <i class="gwi gwi_c-telegram scl__ico-c-telegram"></i>
                        </a>
                        <a href="index.html#Discord" target="_blank" class="scl__item">
                            <i class="gwi gwi_discord scl__ico-discord"></i>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>            <div class="navigation__box navigation__box_side_right" data-place-to="auth">
                <div class="navigation__lang lang notranslate">
    <div class="lang__current">
        <div class="lang__name" data-current-lang>vn</div>
    </div>
    <div class="lang__list">
        <a href="index" class="lang__link lang__link_sub" data-google-lang="vi">
            <div class="lang__name">vn</div>
        </a>
        <a href="../en/index" class="lang__link lang__link_sub" data-google-lang="en">
            <div class="lang__name">en</div>
        </a>
    </div>
</div>


                
            </div>

            <div class="navigation__gw-burger gw-burger" data-gw-burger>
                <div class="gw-burger__box">
                    <div class="gw-burger__line gw-burger__line_pos_top"></div>
                    <div class="gw-burger__line gw-burger__line_pos_middle"></div>
                    <div class="gw-burger__line gw-burger__line_pos_bottom"></div>
                </div>
            </div>
        </div>
        <!-- END  navigation -->
    </div>
</div>
<!-- END  section -->

    <div class="page">

                    <div class="bg">
                <img src="../template/site/l2gve/images/bg/bg-full.jpg" alt="bg" class="bg__img" aria-hidden="true" />
            </div>

            <header class="section" data-section="header" data-target-section="header" data-target-section-offset="0" id="header">
    <div class="header-scl-box" data-place-to="scl" data-gw-anime="fadeInRight" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s"></div>
    <div class="container" data-container="header">
        <div class="header">
            <div class="header__box">

                <div class="header__content" data-gw-anime="fadeInUp" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                    <div class="header__title">Essence GvE 100 - August 16!</div>
                    <div class="header__desc">
                        The Essence GvE server complex presents a new project with x100 rates and a unique concept in the world of Lineage 2                    </div>
                </div>
                <div class="header__btns" data-gw-anime="fadeInDown" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                    <a href="download" class="button">Download</a>
                </div>
            </div>
        </div>
        <!-- END  header -->
    </div>
    <!-- END  container -->
</header>
<!-- END  section -->          



                
    
    
    

    
    
    
                

                
    
    
    


<section class="section" data-section="news" data-target-section="news" data-target-section-offset="0" id="news">
    <div class="container" data-container="news">
        <div class="news" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="rating__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h2 class="heading__title">Infomations</h2>
                <div class="heading__desc">Server information</div>
            </div>
            <div class="news__box" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <div class="news__control">
                    <div class="news__btns">
                        <a href="all-news" class="btn btn_size_small btn_accent_no">
                            Tất cả                        </a>
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="news">
                            News                        </a>
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="promotions">
                            Promotions                        </a>
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="features">
                            Features                        </a>
                    </div>
                    <div class="news__arrows arrows">
                        <div class="news__arrow news__arrow_prev arrow arrow_prev" data-slider-prev="news"></div>
                        <div class="news__arrow news__arrow_next arrow arrow_next" data-slider-next="news"></div>
                    </div>
                </div>

                <div class="news__wrap">
                    <div class="news__list" data-slider="news">
                        <div class="swiper-wrapper" data-news-list></div>
                    </div>
                </div>
            </div>
            <!-- <div class="news__dots dots" data-slider-dots="news"></div> -->
        </div>
        <!-- END  news -->
    </div>
    <!-- END  container -->
</section>
<!-- END  section -->


<script>
// Enhanced news loading with better error handling and performance
(function() {
    let newsCache = null;
    let newsLoading = false;

    async function loadNews(type = 'all', limit = 10) {
        if (newsLoading) return;

        const cacheKey = `news_${type}_${limit}`;

        // Check cache first (5 minutes cache)
        if (newsCache && newsCache.key === cacheKey &&
            Date.now() - newsCache.timestamp < 5 * 60 * 1000) {
            __config.posts = newsCache.data;
            updateNewsDisplay();
            return;
        }

        newsLoading = true;

        try {
            const url = `../api/news.php?limit=${limit}${type !== 'all' ? `&type=${type}` : ''}`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (Array.isArray(data) && data.length > 0) {
                // Cache the result
                newsCache = {
                    key: cacheKey,
                    data: data,
                    timestamp: Date.now()
                };

                __config.posts = data;

                // Trigger news display update
                if (typeof updateNewsDisplay === 'function') {
                    updateNewsDisplay();
                } else if (typeof newsInit === 'function') {
                    newsInit();
                }

                // Dispatch custom event for other components
                document.dispatchEvent(new CustomEvent('newsLoaded', {
                    detail: { news: data, type: type }
                }));

            } else {
                // Use fallback data if no news from API
                throw new Error('No news data available');
            }

        } catch (error) {
            console.error('Error loading news:', error);

            // Fallback data với tin tức mẫu
            const fallbackNews = [
                {
                    id: 1,
                    title: "THÔNG TIN DỰ ÁN INTERLUDE X30",
                    desc: "Các khía cạnh game được chế tác cẩn thận. Shop đến B-grade. Buffer với tùy chọn buff premium...",
                    content: "Nội dung đầy đủ ở đây...",
                    img: "../template/site/l2gve/images/post/img-def-0.jpg",
                    type: "news",
                    date: "2024-08-01 12:00:00"
                },
                {
                    id: 2,
                    title: "BETA TEST CHÍNH THỨC - 12 THÁNG 8",
                    desc: "Ngày 12 tháng 8 bắt đầu beta test mở của máy chủ L2GVE...",
                    content: "Nội dung đầy đủ ở đây...",
                    img: "../template/site/l2gve/images/post/img-def-0.jpg",
                    type: "promotions",
                    date: "2024-08-12 10:00:00"
                }
            ];

            __config.posts = fallbackNews;

            // Trigger news display update with fallback data
            if (typeof updateNewsDisplay === 'function') {
                updateNewsDisplay();
            } else if (typeof newsInit === 'function') {
                newsInit();
            }

            // Dispatch event with fallback data
            document.dispatchEvent(new CustomEvent('newsLoaded', {
                detail: { news: fallbackNews, type: type, fallback: true }
            }));
        } finally {
            newsLoading = false;
        }
    }

    // Make loadNews globally available
    window.loadNews = loadNews;

    // Custom news display function
    function updateNewsDisplay() {
        const newsContainer = document.querySelector('[data-news-list]');
        if (!newsContainer || !__config.posts) return;

        const newsItems = __config.posts.map(item => {
            const readMoreUrl = `news-detail.html?id=${item.id}`;

            return `
                <div class="swiper-slide">
                    <div class="news__item">
                        <div class="news__img">
                            <img src="${item.img || item.image || '../template/site/l2gve/images/post/img-def-0.jpg'}" alt="${item.title}">
                        </div>
                        <div class="news__content">
                            <div class="news__meta">
                                <span class="news__type">${item.type}</span>
                                <span class="news__date">${item.date || item.created_at}</span>
                            </div>
                            <h3 class="news__title">${item.title}</h3>
                            <p class="news__desc">${item.desc || item.description}</p>
                            <a href="${readMoreUrl}" class="news__link btn btn_size_small btn_accent">
                                Đọc thêm
                            </a>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        newsContainer.innerHTML = newsItems;

        // Reinitialize Swiper if it exists
        if (typeof newsSlider !== 'undefined' && newsSlider) {
            newsSlider.update();
        }

        // Dispatch event to notify other components
        document.dispatchEvent(new CustomEvent('newsDisplayUpdated'));
    }

    // Setup filter buttons
    function setupNewsFilters() {
        const filterButtons = document.querySelectorAll('[data-sn-btn]');

        filterButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('btn_accent'));
                filterButtons.forEach(btn => btn.classList.add('btn_accent_no'));

                // Add active class to clicked button
                this.classList.remove('btn_accent_no');
                this.classList.add('btn_accent');

                // Load news with selected filter
                const filterType = this.getAttribute('data-sn-btn');
                loadNews(filterType);
            });
        });

        // Set "All" as default active
        const allButton = document.querySelector('[data-sn-btn="all"]');
        if (allButton) {
            allButton.classList.remove('btn_accent_no');
            allButton.classList.add('btn_accent');
        }
    }

    // Auto-load news when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setupNewsFilters();
            loadNews();
        });
    } else {
        setupNewsFilters();
        loadNews();
    }

    // Reload news when coming back from background (page visibility API)
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden && newsCache &&
            Date.now() - newsCache.timestamp > 10 * 60 * 1000) { // 10 minutes
            loadNews();
        }
    });



    // Initialize modal handlers
    function initModalHandlers() {
        // Initialize fancybox modals if available
        if (typeof $.fancybox !== 'undefined') {
            $('[data-fancybox]').fancybox({
                buttons: ['close'],
                animationEffect: 'fade',
                transitionEffect: 'slide'
            });
        }

        // Initialize custom modals
        document.querySelectorAll('[data-modal]').forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                const modalId = this.getAttribute('data-modal');
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'flex';
                    modal.classList.add('modal-open');
                }
            });
        });

        // Close modal handlers
        document.querySelectorAll('.ww__close, [data-modal-close]').forEach(closeBtn => {
            closeBtn.addEventListener('click', function() {
                const modal = this.closest('.ww');
                if (modal) {
                    modal.classList.remove('modal-open');
                    setTimeout(() => {
                        modal.style.display = 'none';
                    }, 300);
                }
            });
        });

        // Close modal on backdrop click
        document.querySelectorAll('.ww').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('modal-open');
                    setTimeout(() => {
                        this.style.display = 'none';
                    }, 300);
                }
            });
        });
    }

    // Initialize everything when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, initializing components...');

        // Initialize modal functionality
        initModalHandlers();

        console.log('All components initialized');
    });






})();
</script>

            <section class="section" data-section="rating" data-target-section="rating" data-target-section-offset="0" id="rating">
            <div class="container" data-container="rating">
        <!-- <div class="rating" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="rating__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h2 class="heading__title">Rating</h2>
                <div class="heading__desc">Server information</div>
            </div>
            <div class="rating__box" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
             
                <div class="rating__btns">
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-pvp" data-open-tab-group="servers" data-open-tab-active="true">
                        Top PVP                    </a>
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-pk" data-open-tab-group="servers">
                        Top PK                    </a>
                    
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-clan" data-open-tab-group="servers">
                        Clans                    </a>
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-exp" data-open-tab-group="servers">
                        Top EXP                    </a>
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-castle" data-open-tab-group="servers">
                        Castles                    </a>
                </div>
                <div class="rating__tabs">
                                                                            </div>
                <div class="rating__links">
                    <a href="rating.html" class="btn btn_size_large"> Full rating </a>
                </div>
            </div>
        </div> -->
        <!-- END  rating -->
    </div>
    <!-- END  container -->
</section>
<!-- END  section -->            <section class="section" data-section="community" data-target-section="community" data-target-section-offset="0" id="community">
    <div class="container" data-container="community">
        <div class="community" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="community__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h2 class="heading__title">Community</h2>
                <div class="heading__desc">Social Networks</div>
            </div>
            <div class="community__box" style="justify-content: center; display: flex;">
                <div class="community__container" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                    <div class="community__social-list">
                        <a href="index.html#Telegram" target="_blank" class="social decbox" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                            <div class="social__pic">
                                <i class="gwi gwi_c-telegram social__c-telegram"></i>
                            </div>
                            <div class="social__title">Telegram</div>
                            <div class="social__desc">Contact us if you have any questions</div>
                        </a>
                        <a href="index.html#Discord" target="_blank" class="social decbox" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                            <div class="social__pic">
                                <i class="gwi gwi_discord social__discord"></i>
                            </div>
                            <div class="social__title">discord</div>
                            <div class="social__desc">Contact us if you have any questions</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>              
<!-- END  section -->        
       

    </div> <!-- END page -->

    <!-- Back to Top Button -->
    <button id="backToTop" class="back-to-top" aria-label="Về đầu trang">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 19V5M5 12L12 5L19 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    </button>

        <ul class="points" data-points>
    <div class="points__overlay"></div>
    <li class="point point_active" data-point-name="header">
        <div class="point__dot"></div>
        <div class="point__name">Home</div>
    </li>
    <li class="point" data-point-name="news">
        <div class="point__dot"></div>
        <div class="point__name">News</div>
    </li>
    <li class="point" data-point-name="community">
        <div class="point__dot"></div>
        <div class="point__name">Community</div>
    </li>
</ul>    
    

















































<!-- downloads START -->
<div style="display: none">
    <div class="ww ww_animated" id="wow">
        <div class="ww__inner">
            <div class="ww__body downloads">
                <div class="ww__close" data-fancybox-close></div>
                <div class="ww__heading heading heading_short">
                    <h2 class="heading__title">Start playing</h2>
                    <div class="heading__desc">Game files</div>
                </div>
                <div class="downloads__list">
                    <div class="downloads__box">
                        <div class="downloads__title">GAME CLIENT</div>
                        <div class="downloads__btns">
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent">GOOGLE DISK</a>
                            <!-- <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">FEX.NET</a>
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Mega</a>
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Torrent</a> -->
                        </div>
                    </div>
                        <div class="downloads__box">
                            <div class="downloads__title">UPDATER</div>
                            <div class="downloads__btns">
                                <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent">GOOGLE DISK</a>
                                <!-- <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">FEX.NET</a>
                                <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Mega</a>
                                <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Torrent</a> -->
                            </div>
                        </div>
                    <div class="downloads__box">
                        <div class="downloads__title">SYSTEM PATCH</div>
                        <div class="downloads__btns">
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent">GOOGLE DISK</a>
                            <!-- <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">FEX.NET</a>
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Mega</a>
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Torrent</a> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- downloads END -->
<div data-streams-overlay data-streams-open></div>
    <!-- Simplified Script Loading for Better Performance -->

    <!-- Critical scripts loaded synchronously -->
    <script src="../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js"></script>
    <script src="../template/site/l2gve/libs/js-cookie/js/js.cookie.min.js"></script>
    <script src="../template/site/l2gve/libs/gsap/js/gsap.min.js"></script>
    <script src="../template/site/l2gve/libs/gsap/js/ScrollTrigger.min.js"></script>
    <script src="../template/site/l2gve/libs/gsap/js/ScrollToPlugin.min.js"></script>

    <!-- Secondary scripts loaded with defer -->
    <script src="../template/site/l2gve/libs/swiper/js/swiper.min.js" defer></script>
    <script src="../template/site/l2gve/libs/fancybox/js/jquery.fancybox.js" defer></script>

    <!-- Non-critical scripts loaded asynchronously -->
    <!-- <script src="../template/site/l2gve/libs/google-translate/js/google-translate.js" async></script> -->
    <script src="../template/site/l2gve/libs/insertmedia/js/insertmedia.min.js" async></script>
    <script src="../template/site/l2gve/libs/countdown/js/jquery.plugin.min.js" async></script>
    <script src="../template/site/l2gve/libs/countdown/js/jquery.countdown.min.js" async></script>

    <!-- Main app scripts -->
    <script src="../template/site/l2gve/js/clean-app.js"></script>
    <script src="../template/site/l2gve/js/custom.js@v=1714752167"></script>

<script>
    // Enhanced Back to Top functionality with scroll fix
    document.addEventListener('DOMContentLoaded', function() {
        const backToTopBtn = document.getElementById('backToTop');

        if (!backToTopBtn) {
            console.warn('Back to Top button not found');
            return;
        }

        // Fix any scroll blocking elements
        document.body.style.overflowX = 'hidden';
        document.documentElement.style.scrollBehavior = 'smooth';

        // Fix community section scroll issues
        const communityBox = document.querySelector('.community__box');
        if (communityBox) {
            communityBox.style.pointerEvents = 'none';

            // Allow pointer events on child elements
            const childElements = communityBox.querySelectorAll('*');
            childElements.forEach(el => {
                if (el.tagName === 'A' || el.classList.contains('social')) {
                    el.style.pointerEvents = 'auto';
                }
            });
        }

        // Throttle scroll events for better performance
        let ticking = false;
        function updateBackToTop() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // Show button when scrolled down 20% of page or past 300px
            const showThreshold = Math.min(300, documentHeight * 0.2);

            if (scrollTop > showThreshold) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }

            ticking = false;
        }

        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (!ticking) {
                requestAnimationFrame(updateBackToTop);
                ticking = true;
            }
        });

        // Smooth scroll to top when clicked
        backToTopBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Custom smooth scroll animation with slower speed
            const duration = 1500; // Tăng từ 800ms lên 1500ms để chậm hơn
            const start = window.pageYOffset;
            const startTime = performance.now();

            const animateScroll = (currentTime) => {
                const timeElapsed = currentTime - startTime;
                const progress = Math.min(timeElapsed / duration, 1);

                // Easing function for smoother animation
                const easeInOutCubic = progress => progress < 0.5
                    ? 4 * progress * progress * progress
                    : 1 - Math.pow(-2 * progress + 2, 3) / 2;

                window.scrollTo(0, start * (1 - easeInOutCubic(progress)));

                if (progress < 1) {
                    requestAnimationFrame(animateScroll);
                }
            };

            requestAnimationFrame(animateScroll);
        });

        // Initial check
        updateBackToTop();
    });
    </script>

    <style>
        /* Modal styles */
        .ww {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .ww.modal-open {
            opacity: 1;
        }

        .ww__inner {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 20px;
        }

        .ww__body {
            background: rgba(0, 0, 0, 0.9);
            border-radius: 10px;
            padding: 30px;
            max-width: 600px;
            width: 100%;
            position: relative;
            transform: scale(0.8);
            transition: transform 0.3s ease;
        }

        .ww.modal-open .ww__body {
            transform: scale(1);
        }

        .ww__close {
            position: absolute;
            top: 15px;
            right: 20px;
            color: #fff;
            font-size: 24px;
            cursor: pointer;
            z-index: 10001;
        }

        .ww__close:hover {
            color: #ffd700;
        }

        [data-streams-overlay] {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9998;
            display: none;
        }
    </style>

</body>

</html>

<!-- 
Copyright © l2gve.com
Design: Unsimple 
Front-End Developer: Front-end developer: Vitalii P. |  Get-Web.Site
 -->
