<?php
declare(strict_types=1);

// PHP 8.4 Advanced JIT optimizations
// Note: OPcache settings should be configured in php.ini, not runtime
// These settings are for reference only

// Check if OPcache is available and enabled
if (function_exists('opcache_get_status')) {
    $opcache_status = opcache_get_status();
    if ($opcache_status && $opcache_status['opcache_enabled']) {
        // OPcache is working - no need to set runtime options
        define('OPCACHE_ENABLED', true);
    } else {
        define('OPCACHE_ENABLED', false);
    }
} else {
    define('OPCACHE_ENABLED', false);
}

// PHP 8.4 Enhanced performance settings (safe runtime settings)
ini_set('realpath_cache_size', '8192K'); // Doubled cache size
ini_set('realpath_cache_ttl', '1200'); // Longer TTL for better caching
ini_set('max_execution_time', '60'); // Reasonable limit
ini_set('memory_limit', '512M'); // Adequate memory for complex operations

// PHP 8.4 New features optimization
ini_set('zend.exception_ignore_args', '1'); // Reduce memory usage in exceptions
ini_set('zend.exception_string_param_max_len', '15'); // Limit exception string length

// Error handling
error_reporting(E_ALL);
ini_set('display_errors', '0');
ini_set('log_errors', '1');
ini_set('log_errors_max_len', '1024');

// Security enhancements
ini_set('expose_php', '0');

// OPcache configuration reference (should be set in php.ini):
/*
[opcache]
opcache.enable=1
opcache.enable_cli=1
opcache.jit_buffer_size=512M
opcache.jit=1255
opcache.memory_consumption=512
opcache.max_accelerated_files=50000
opcache.revalidate_freq=0
opcache.validate_timestamps=0
opcache.save_comments=0
opcache.enable_file_override=1
opcache.optimization_level=0x7FFEBFFF
*/
ini_set('session.cookie_httponly', '1');
ini_set('session.cookie_secure', '1');
ini_set('session.use_strict_mode', '1');
ini_set('session.cookie_samesite', 'Strict');