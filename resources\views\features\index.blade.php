@extends('layouts.app')

@section('title', __('messages.Features') . ' - L2GVE')
@section('description', 'Discover the amazing features of L2GVE Lineage 2 server')

@section('content')
<section class="section" data-section="features" data-target-section="features" data-target-section-offset="0" id="features">
    <div class="container" data-container="features">
        <div class="features" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="features__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h1 class="heading__title">{{ __('messages.Features') }}</h1>
                <div class="heading__dec">
                    <img src="{{ asset('template/site/l2gve/images/heading/dec.png') }}" alt="decoration" class="heading__dec-img">
                </div>
            </div>
            
            <div class="features__content">
                @if($features->count() > 0)
                    <div class="features__list">
                        @foreach($features as $feature)
                        <article class="post">
                            <div class="post__bg" style="background-image: url('{{ $feature->image ?: asset('template/site/l2gve/images/post/img-def-0.jpg') }}')"></div>
                            <div class="post__container">
                                <div class="post__date">
                                    <span class="post__date-accent">{{ $feature->created_at->format('d') }}</span>
                                    {{ $feature->created_at->format('M Y') }}
                                </div>
                                <h2 class="post__title">{{ $feature->title }}</h2>
                                <div class="post__desc">{{ $feature->excerpt }}</div>
                                <div class="post__btns">
                                    <a href="{{ route('features.show', ['locale' => $locale, 'id' => $feature->id]) }}" class="btn">
                                        {{ __('messages.Read More') }}
                                    </a>
                                </div>
                            </div>
                            <a href="{{ route('features.show', ['locale' => $locale, 'id' => $feature->id]) }}" class="post__link"></a>
                        </article>
                        @endforeach
                    </div>
                    
                    <!-- Pagination -->
                    <div class="features__pagination">
                        {{ $features->links() }}
                    </div>
                @else
                    <div class="features__empty">
                        <p>{{ __('messages.No features available at the moment.') }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
// Features page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Add any features-specific functionality here
});
</script>
@endsection
