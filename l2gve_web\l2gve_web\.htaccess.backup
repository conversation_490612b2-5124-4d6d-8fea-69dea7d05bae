# Basic Apache configuration for L2GVE
# Removed PHP settings that may cause issues in XAMPP

# Enable directory browsing for development
Options +Indexes +FollowSymLinks

# Allow access to all files
<RequireAll>
    Require all granted
</RequireAll>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain text/html text/xml text/css
    AddOutputFilterByType DEFLATE application/xml application/xhtml+xml application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript application/x-javascript
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Advanced Cache headers for PHP 8.4 optimization
<IfModule mod_expires.c>
    ExpiresActive On

    # HTML - Short cache for dynamic content
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/xhtml+xml "access plus 1 hour"

    # CSS and JavaScript - Long cache with versioning
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"

    # Images - Long cache
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"

    # Fonts - Very long cache
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/eot "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"

    # Other assets
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
</IfModule>

# Cache-Control headers for better performance
<IfModule mod_headers.c>
    # Remove ETags (we use Last-Modified instead)
    Header unset ETag

    # Cache-Control for different file types
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
        Header set Vary "Accept-Encoding"
    </FilesMatch>

    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "public, max-age=3600, must-revalidate"
        Header set Vary "Accept-Encoding, Accept"
    </FilesMatch>

    # Security headers
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Performance headers
    Header set Connection "Keep-Alive"
    Header set Keep-Alive "timeout=5, max=100"
</IfModule>

# ETags
FileETag MTime Size

# Rewrite rules
RewriteEngine On

# Route wiki pages through PHP
RewriteRule ^en/wiki$ wiki.php [L]
RewriteRule ^vn/wiki$ wiki.php [L]

# API routes
RewriteRule ^api/(.*)$ api/$1 [L]

# Admin routes
RewriteRule ^admin/(.*)$ admin/$1 [L]

# Force redirect root to /en (but don't interfere with direct access)
RewriteRule ^$ en/ [R=301,L]
RewriteRule ^index\.(php|html)$ en/ [R=301,L]

# Allow direct access to /en/ and /vn/ directories
# (Remove the problematic rewrite rules that were causing issues)









