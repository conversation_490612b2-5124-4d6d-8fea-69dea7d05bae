/* custom.css */

/* News Section Styles */
.news__box {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.news__control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 0 20px;
}

.news__btns {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.news__btns .btn {
    padding: 8px 16px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.news__btns .btn:hover,
.news__btns .btn.active {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-color: #ff6b35;
    transform: translateY(-2px);
}

.news__arrows {
    display: flex;
    gap: 10px;
}

.news__arrow {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
}

.news__arrow:hover {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-color: #ff6b35;
    transform: translateY(-2px);
}

.news__arrow:before {
    content: '';
    width: 8px;
    height: 8px;
    border-top: 2px solid #fff;
    border-right: 2px solid #fff;
    transform: rotate(-45deg);
}

.news__arrow_prev:before {
    transform: rotate(-135deg);
    margin-left: 2px;
}

.news__arrow_next:before {
    transform: rotate(45deg);
    margin-right: 2px;
}

.news__wrap {
    position: relative;
    overflow: hidden;
}

.news__list {
    position: relative;
}

.news__item {
    background: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.news__item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.news__item-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.news__item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news__item:hover .news__item-image img {
    transform: scale(1.05);
}

.news__item-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.news__item-title {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.3;
}

.news__item-excerpt {
    color: #ccc;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    flex: 1;
}

.news__item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 12px;
    color: #999;
}

.news__item-date {
    color: #ff6b35;
}

.news__item-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    align-self: flex-start;
}

.news__item-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
    color: #fff;
    text-decoration: none;
}

/* Responsive */
@media (max-width: 768px) {
    .news__control {
        flex-direction: column;
        gap: 20px;
        padding: 0 10px;
    }

    .news__btns {
        justify-content: center;
    }

    .news__item-content {
        padding: 15px;
    }

    .news__item-title {
        font-size: 16px;
    }

    .news__item-excerpt {
        font-size: 13px;
    }
}
