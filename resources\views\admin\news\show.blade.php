@extends('admin.layout')

@section('title', 'View Article')

@section('styles')
<style>
.article-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.article-title {
    font-size: 2rem;
    font-weight: bold;
    color: #fff;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.article-image {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.article-content {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 2rem;
    color: #fff;
    line-height: 1.6;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
    color: #fff;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.article-content p {
    margin-bottom: 1rem;
}

.article-content ul,
.article-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.article-content blockquote {
    border-left: 4px solid var(--accent-color);
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: rgba(255, 255, 255, 0.9);
}

.article-content code {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.9em;
}

.article-content pre {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1rem 0;
}

.action-buttons {
    position: sticky;
    top: 20px;
    z-index: 10;
}

.stats-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-color);
}

.stats-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

@media (max-width: 768px) {
    .article-title {
        font-size: 1.5rem;
    }
    
    .article-header {
        padding: 1.5rem;
    }
    
    .article-content {
        padding: 1.5rem;
    }
}
</style>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-eye me-2"></i>View Article
    </h1>
    <div>
        <a href="{{ route('news.show', ['locale' => 'en', 'id' => $news->id]) }}" target="_blank" class="btn btn-outline-info me-2">
            <i class="fas fa-external-link-alt me-2"></i>View Live
        </a>
        <a href="{{ route('admin.news.index') }}" class="btn btn-outline-light">
            <i class="fas fa-arrow-left me-2"></i>Back to News
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Article Header -->
        <div class="article-header">
            <h1 class="article-title">{{ $news->title }}</h1>
            
            <div class="article-meta">
                <div class="meta-item">
                    <i class="fas fa-tag"></i>
                    <span class="badge bg-info">{{ ucfirst($news->type) }}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-circle"></i>
                    <span class="badge bg-{{ $news->status === 'active' ? 'success' : 'warning' }}">
                        {{ $news->status === 'active' ? 'Published' : 'Draft' }}
                    </span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <span>{{ $news->created_at->format('M d, Y') }}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span>{{ $news->updated_at->diffForHumans() }}</span>
                </div>
            </div>

            @if($news->description)
                <div class="article-excerpt">
                    <p class="lead text-muted">{{ $news->description }}</p>
                </div>
            @endif
        </div>

        <!-- Featured Image -->
        @if($news->image)
            <img src="{{ $news->image }}" alt="{{ $news->title }}" class="article-image">
        @endif

        <!-- Article Content -->
        <div class="article-content">
            @if($news->content)
                {!! $news->content !!}
            @else
                <p class="text-muted text-center py-4">
                    <i class="fas fa-file-alt fa-3x mb-3 opacity-50"></i><br>
                    No content available for this article.
                </p>
            @endif
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Action Buttons -->
        <div class="action-buttons">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.news.edit', $news->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Edit Article
                        </a>
                        <a href="{{ route('news.show', ['locale' => 'en', 'id' => $news->id]) }}" target="_blank" class="btn btn-info">
                            <i class="fas fa-external-link-alt me-2"></i>View Live
                        </a>
                        <hr>
                        <form method="POST" action="{{ route('admin.news.destroy', $news->id) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this article? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-2"></i>Delete Article
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Article Stats -->
            <div class="stats-card">
                <div class="stats-number">{{ $news->id }}</div>
                <div class="stats-label">Article ID</div>
            </div>

            <div class="stats-card">
                <div class="stats-number">{{ str_word_count(strip_tags($news->content ?? '')) }}</div>
                <div class="stats-label">Word Count</div>
            </div>

            <div class="stats-card">
                <div class="stats-number">{{ strlen(strip_tags($news->content ?? '')) }}</div>
                <div class="stats-label">Characters</div>
            </div>

            <!-- Article Details -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Article Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <small class="text-muted">Full Title</small>
                            <div class="small">{{ $news->title }}</div>
                        </div>
                        @if($news->description)
                            <div class="col-12">
                                <small class="text-muted">Description</small>
                                <div class="small">{{ $news->description }}</div>
                            </div>
                        @endif
                        <div class="col-6">
                            <small class="text-muted">Type</small>
                            <div class="small">{{ ucfirst($news->type) }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Status</small>
                            <div class="small">{{ $news->status === 'active' ? 'Published' : 'Draft' }}</div>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">Created</small>
                            <div class="small">{{ $news->created_at->format('F j, Y \a\t g:i A') }}</div>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">Last Modified</small>
                            <div class="small">{{ $news->updated_at->format('F j, Y \a\t g:i A') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>
@endsection
