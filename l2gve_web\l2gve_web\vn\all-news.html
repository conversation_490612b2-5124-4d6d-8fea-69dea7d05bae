<!DOCTYPE html>
<html lang="vi">

<head>
    <!-- Load bypass script FIRST -->
    <script src="../bypass-protection.js"></script>

    <!-- Preload critical resources -->
    <link rel="preload" href="../template/site/l2gve/js/clean-app.js" as="script">
    <link rel="preload" href="../template/site/l2gve/js/custom.js@v=1714752167" as="script">
    <link rel="preload" href="../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js" as="script">

    <meta charset="UTF-8">
    <title>Tất cả tin tức - L2GVE</title>
    <meta name="Description" content="Tất cả tin tức và cập nhật từ máy chủ L2GVE Essence GvE x100">
    <meta name="Keywords" content="L2GVE tin tức, Lineage 2 tin tức, cập nhật máy chủ">
       
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="theme-color" content="#0f1416">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="viewport" content="width=device-width">

    <link rel="shortcut icon" href="../template/site/l2gve/images/favicon/favicon.ico@v=1" type="image/x-icon">

    <!-- fonts -->
    <link rel="stylesheet" href="../template/site/l2gve/fonts/beaufortforlol/fonts.css" />
    <link rel="stylesheet" href="../template/site/l2gve/fonts/intro/stylesheet.css" />

    <!-- libs -->
    <link rel="stylesheet" href="../template/site/l2gve/libs/gwi/css/gwi.css" />
    <link rel="stylesheet" href="../template/site/l2gve/libs/fancybox/css/jquery.fancybox.min.css" />

    <!-- Main style -->
    <link rel="stylesheet" href="../template/site/l2gve/css/main.css@v=1719174774.css">
    <link rel="stylesheet" href="../template/site/l2gve/css/custom.css@v=1714752167.css">

    <style>
        .all-news {
            padding: 100px 0 50px;
            min-height: 100vh;
        }

        .all-news__header {
            text-align: center;
            margin-bottom: 50px;
        }

        .all-news__title {
            font-size: 48px;
            color: #fff;
            margin-bottom: 20px;
        }

        .all-news__subtitle {
            font-size: 18px;
            color: #ccc;
            margin-bottom: 40px;
        }

        .all-news__filters {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 50px;
            flex-wrap: wrap;
        }



        .all-news__content {
            margin-bottom: 50px;
        }

        .category-section {
            margin-bottom: 50px;
        }

        .category-title {
            font-size: 28px;
            color: #fff;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007cba;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .category-count {
            background: #007cba;
            color: #fff;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 500;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .pagination-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #ccc;
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 40px;
            text-align: center;
        }

        .pagination-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
        }

        .pagination-btn.active {
            background: #007cba;
            color: #fff;
            border-color: #007cba;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-info {
            color: #ccc;
            font-size: 14px;
            margin: 0 15px;
        }

        .all-news__grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .news-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .news-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .news-card__image {
            width: 100%;
            height: 220px;
            overflow: hidden;
            position: relative;
        }

        .news-card__image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .news-card:hover .news-card__image img {
            transform: scale(1.05);
        }

        .news-card__content {
            padding: 25px !important;
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
        }

        .news-card__meta {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            margin-bottom: 15px !important;
            gap: 10px !important;
            flex-wrap: nowrap !important;
            width: 100% !important;
            position: relative !important;
        }

        .news-card__meta .news-card__type {
            flex-shrink: 0;
        }

        .news-card__type {
            background: #007cba !important;
            color: white !important;
            padding: 4px 12px !important;
            border-radius: 15px !important;
            text-transform: uppercase !important;
            font-size: 11px !important;
            font-weight: 600 !important;
            white-space: nowrap !important;
            display: inline-block !important;
            min-width: fit-content !important;
            width: auto !important;
            height: auto !important;
            line-height: 1.2 !important;
            max-width: none !important;
            text-overflow: initial !important;
            overflow: visible !important;
            vertical-align: top !important;
            flex: none !important;
            flex-shrink: 0 !important;
            flex-grow: 0 !important;
        }

        /* Force override any external CSS */
        .news-card__meta .news-card__type,
        .news-card .news-card__type,
        span.news-card__type,
        .news-type-fixed {
            display: inline-block !important;
            width: auto !important;
            height: auto !important;
            max-width: none !important;
            min-width: fit-content !important;
            flex: none !important;
            flex-shrink: 0 !important;
            flex-grow: 0 !important;
            flex-basis: auto !important;
        }

        /* Fix for .news class that might be affecting layout */
        .news {
            min-height: auto !important;
        }

        /* Ensure news cards display properly */
        .news-card {
            display: block !important;
        }

        .news-card__type.promotions {
            background: #e74c3c;
        }

        .news-card__type.features {
            background: #27ae60;
        }

        .news-card__date {
            color: #999;
            font-size: 13px;
        }

        .news-card__title {
            font-size: 20px;
            margin-bottom: 15px;
            color: #fff;
            line-height: 1.4;
            font-weight: 600;
        }

        .news-card__desc {
            color: #ccc;
            margin-bottom: 20px;
            line-height: 1.6;
            font-size: 14px;
        }

        .news-card__footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .news-card__read-more {
            background: linear-gradient(135deg, #007cba, #0056b3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .news-card__read-more:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 124, 186, 0.4);
        }

        .back-to-top {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #007cba, #0056b3);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .back-to-top:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 124, 186, 0.4);
        }

        .back-to-top i {
            margin-right: 10px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #ccc;
        }

        .no-news {
            text-align: center;
            padding: 50px;
            color: #999;
        }

        /* Language navigation enhancement */
        .lang__current .lang__name {
            font-weight: bold !important;
            color: #ffffff !important;
        }

        .lang__link .lang__name {
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .lang__link:hover .lang__name {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .all-news__title {
                font-size: 36px;
            }

            .all-news__grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .all-news__filters {
                gap: 10px;
            }
        }
    </style>
</head>

<body class="body">
    <div class="bg">
        <img src="../template/site/l2gve/images/bg/bg-repeat.jpg" alt="bg" class="bg__img" aria-hidden="true" />
    </div>

    <!-- Navigation -->
    <div class="section compensate-for-scrollbar" data-section="navigation">
    <div class="container" data-container="navigation">
        <div class="navigation">
            <div class="navigation__box navigation__box_side_left">
                <a href="index" class="logo">
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img" style="width: 9rem" />
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img logo__img_hover" />
                </a>

            </div>

           <div class="navigation__menu menu" data-menu>
    <div class="menu__content">
        <ul class="menu__list">
            <li class="menu__el">
                <a href="index" class="menu__item" data-menu-close>
                    Trang chủ
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="all-news" class="menu__item" data-menu-close>
                    Tin tức
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="features" class="menu__item" data-menu-close>
                    Tính năng
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="download" class="menu__item" data-menu-close>
                    Tải game
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="#discord" class="menu__item" data-menu-close>
                    Discord
                </a>
            </li>
            <li class="menu__el menu__el_continer menu__el_desktop_none" data-place-from="scl">
                <div class="scl" data-place-container="scl">
                    <div class="scl__list">
                        <a href="index.html#Telegram" target="_blank" class="scl__item">
                            <i class="gwi gwi_c-telegram scl__ico-c-telegram"></i>
                        </a>
                        <a href="index.html#Discord" target="_blank" class="scl__item">
                            <i class="gwi gwi_discord scl__ico-discord"></i>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>            <div class="navigation__box navigation__box_side_right" data-place-to="auth">
                <div class="navigation__lang lang notranslate">
    <div class="lang__current">
        <div class="lang__name" data-current-lang>vn</div>
    </div>
    <div class="lang__list">
        <a href="all-news" class="lang__link lang__link_sub" data-google-lang="vi">
            <div class="lang__name">vn</div>
        </a>
        <a href="../en/all-news" class="lang__link lang__link_sub" data-google-lang="en">
            <div class="lang__name">en</div>
        </a>
    </div>
</div>



            </div>

            <div class="navigation__gw-burger gw-burger" data-gw-burger>
                <div class="gw-burger__box">
                    <div class="gw-burger__line gw-burger__line_pos_top"></div>
                    <div class="gw-burger__line gw-burger__line_pos_middle"></div>
                    <div class="gw-burger__line gw-burger__line_pos_bottom"></div>
                </div>
            </div>
        </div>
        <!-- END  navigation -->
    </div>
</div>
<!-- END  section -->

    <div class="page">
        <section class="section compensate-for-scrollbar" data-section="all-news">
            <div class="container">
                <div class="all-news">
                    <div class="navigation-height-compensate"></div>
                    
                    <div class="all-news__header" style="margin-top: -50px;">
                        <div class="all-news__filters">
                            <button class="btn btn_size_small btn_accent" data-filter="all">Tất cả</button>
                            <button class="btn btn_size_small btn_accent_no" data-filter="news">Tin tức</button>
                            <button class="btn btn_size_small btn_accent_no" data-filter="promotions">Khuyến mãi</button>
                        </div>
                    </div>

                    <div class="all-news__content">
                        <div class="loading" id="loading">
                            <p>Đang tải tin tức...</p>
                        </div>

                        <div id="newsContent" style="display: none;">
                            <!-- News categories will be inserted here -->
                        </div>

                        <div class="pagination" id="pagination" style="display: none;">
                            <!-- Pagination will be inserted here -->
                        </div>

                        <div class="no-news" id="noNews" style="display: none;">
                            <h3>Không tìm thấy tin tức</h3>
                            <p>Hiện tại chưa có tin tức nào được đăng tải.</p>
                        </div>

                        <!-- Back to Top button at bottom -->
                        <div class="back-to-top-wrap" style="text-align: center; margin-top: 50px; padding-bottom: 50px;">
                            <a href="#" class="back-to-top" onclick="scrollToTop(); return false;">
                                <i class="gwi gwi_up"></i> Lên đầu trang
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Scripts -->
    <script src="../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js"></script>
    
    <script>
        let allNews = [];
        let currentFilter = 'all';
        let currentPage = 1;
        const itemsPerPage = 6;

        // Load all news
        async function loadAllNews() {
            try {
                const response = await fetch('../api/news.php?limit=50');
                const data = await response.json();

                if (Array.isArray(data) && data.length > 0) {
                    allNews = data;
                    displayNewsByCategory(allNews);
                } else {
                    showNoNews();
                }
            } catch (error) {
                console.error('Error loading news:', error);
                // Fallback data
                allNews = [
                    {
                        id: 1,
                        title: "MÔ TẢ DỰ ÁN INTERLUDE X30",
                        desc: "Các khía cạnh trò chơi được chế tác cẩn thận. Cửa hàng lên đến B-grade. Buffer với các tùy chọn buff cao cấp. Các sự kiện và tính năng độc đáo đang chờ bạn trong máy chủ thú vị này.",
                        img: "../template/site/l2gve/images/post/img-def-0.jpg",
                        type: "news",
                        date: "2024-08-01 12:00:00"
                    },
                    {
                        id: 2,
                        title: "KIỂM THỬ BETA CHÍNH THỨC - 12 THÁNG 8",
                        desc: "Ngày 12 tháng 8 bắt đầu kiểm thử beta mở của máy chủ L2GVE. Tham gia cùng chúng tôi để kiểm thử độc quyền và trở thành một trong những người đầu tiên trải nghiệm các tính năng độc đáo của chúng tôi.",
                        img: "../template/site/l2gve/images/post/img-def-0.jpg",
                        type: "promotions",
                        date: "2024-08-12 10:00:00"
                    },
                    {
                        id: 3,
                        title: "TÍNH NĂNG TỰ CHỈNH MỚI",
                        desc: "Khám phá các tính năng tùy chỉnh độc đáo giúp L2GVE nổi bật so với các máy chủ khác. Cơ chế gameplay nâng cao và cải tiến chất lượng cuộc sống.",
                        img: "../template/site/l2gve/images/post/img-def-0.jpg",
                        type: "features",
                        date: "2024-08-15 14:30:00"
                    }
                ];
                displayNewsByCategory(allNews);
            }

            hideLoading();
        }

        // Display news by category with pagination
        function displayNewsByCategory(newsArray) {
            const newsContent = document.getElementById('newsContent');

            if (newsArray.length === 0) {
                showNoNews();
                return;
            }

            // Group news by category (excluding features)
            const categories = {
                'news': { title: 'Tin tức', items: [] },
                'promotions': { title: 'Khuyến mãi', items: [] }
            };

            newsArray.forEach(news => {
                // Only include news and promotions, exclude features
                if (categories[news.type] && news.type !== 'features') {
                    categories[news.type].items.push(news);
                }
            });

            // Filter by current filter
            let categoriesToShow = {};
            if (currentFilter === 'all') {
                categoriesToShow = categories;
            } else {
                categoriesToShow[currentFilter] = categories[currentFilter];
            }

            // Calculate pagination
            const allFilteredNews = Object.values(categoriesToShow).reduce((acc, cat) => acc.concat(cat.items), []);
            const totalItems = allFilteredNews.length;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;

            let html = '';

            if (currentFilter === 'all') {
                // Show by categories
                Object.keys(categoriesToShow).forEach(categoryKey => {
                    const category = categoriesToShow[categoryKey];
                    if (category.items.length > 0) {
                        html += `
                            <div class="category-section">
                                <h2 class="category-title">
                                    ${category.title}
                                    <span class="category-count">${category.items.length}</span>
                                </h2>
                                <div class="all-news__grid">
                                    ${category.items.slice(0, 6).map(news => createNewsCard(news)).join('')}
                                </div>
                            </div>
                        `;
                    }
                });
            } else {
                // Show paginated results for specific category
                const paginatedNews = allFilteredNews.slice(startIndex, endIndex);
                const category = categoriesToShow[currentFilter];

                html = `
                    <div class="category-section">
                        <h2 class="category-title">
                            ${category.title}
                            <span class="category-count">${totalItems}</span>
                        </h2>
                        <div class="all-news__grid">
                            ${paginatedNews.map(news => createNewsCard(news)).join('')}
                        </div>
                    </div>
                `;
            }

            newsContent.innerHTML = html;
            newsContent.style.display = 'block';
            document.getElementById('noNews').style.display = 'none';

            // Show pagination only for specific categories
            if (currentFilter !== 'all' && totalPages > 1) {
                displayPagination(totalPages);
            } else {
                document.getElementById('pagination').style.display = 'none';
            }
        }

        // Create news card HTML
        function createNewsCard(news) {
            return `
                <div class="news-card" data-type="${news.type}">
                    <div class="news-card__image">
                        <img src="${news.img || news.image || '../template/site/l2gve/images/post/img-def-0.jpg'}" alt="${news.title}">
                    </div>
                    <div class="news-card__content">
                        <div class="news-card__meta">
                            <span class="news-card__type news-type-fixed ${news.type}" style="display: inline-block !important; width: auto !important; height: auto !important; max-width: none !important; min-width: fit-content !important; padding: 4px 12px !important; white-space: nowrap !important; flex: none !important;">${getTypeLabel(news.type)}</span>
                            <span class="news-card__date">${formatDate(news.date || news.created_at)}</span>
                        </div>
                        <h3 class="news-card__title">${news.title}</h3>
                        <p class="news-card__desc">${truncateText(news.desc || news.description, 120)}</p>
                        <div class="news-card__footer">
                            <a href="news-detail?id=${news.id}" class="news-card__read-more">
                                Đọc thêm
                            </a>
                        </div>
                    </div>
                </div>
            `;
        }

        // Display pagination
        function displayPagination(totalPages) {
            const pagination = document.getElementById('pagination');
            let html = '';

            // Previous button
            html += `<button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">‹</button>`;

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    html += `<button class="pagination-btn active">${i}</button>`;
                } else if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    html += `<button class="pagination-btn" onclick="changePage(${i})">${i}</button>`;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    html += `<span class="pagination-btn">...</span>`;
                }
            }

            // Next button
            html += `<button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">›</button>`;

            // Page info
            const startItem = (currentPage - 1) * itemsPerPage + 1;
            const endItem = Math.min(currentPage * itemsPerPage, allNews.filter(news => currentFilter === 'all' || news.type === currentFilter).length);
            const totalItems = allNews.filter(news => currentFilter === 'all' || news.type === currentFilter).length;

            html += `<div class="pagination-info">Hiển thị ${startItem}-${endItem} trong ${totalItems}</div>`;

            pagination.innerHTML = html;
            pagination.style.display = 'flex';
        }

        // Change page
        function changePage(page) {
            currentPage = page;
            displayNewsByCategory(allNews);
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Get Vietnamese type label
        function getTypeLabel(type) {
            const labels = {
                'news': 'Tin tức',
                'promotions': 'Khuyến mãi',
                'features': 'Tính năng'
            };
            return labels[type] || type;
        }

        // Filter news by type
        function filterNews(type) {
            currentFilter = type;
            currentPage = 1; // Reset to first page

            // Update filter buttons
            document.querySelectorAll('[data-filter]').forEach(btn => {
                btn.classList.remove('btn_accent');
                btn.classList.add('btn_accent_no');
            });

            document.querySelector(`[data-filter="${type}"]`).classList.remove('btn_accent_no');
            document.querySelector(`[data-filter="${type}"]`).classList.add('btn_accent');

            // Display filtered news
            displayNewsByCategory(allNews);
        }

        // Utility functions
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('vi-VN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function truncateText(text, maxLength) {
            if (!text) return '';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showNoNews() {
            document.getElementById('newsContent').style.display = 'none';
            document.getElementById('pagination').style.display = 'none';
            document.getElementById('noNews').style.display = 'block';
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Load news
            loadAllNews();

            // Setup filter buttons
            document.querySelectorAll('[data-filter]').forEach(button => {
                button.addEventListener('click', function() {
                    const filterType = this.getAttribute('data-filter');
                    filterNews(filterType);
                });
            });
        });

        // Scroll to top function with slower animation
        function scrollToTop() {
            const scrollDuration = 1000; // 1 second
            const scrollStep = -window.scrollY / (scrollDuration / 15);

            function scrollAnimation() {
                if (window.scrollY !== 0) {
                    window.scrollBy(0, scrollStep);
                    setTimeout(scrollAnimation, 15);
                }
            }
            scrollAnimation();
        }
    </script>

    <style>
        .gwi_up::before {
            content: "↑";
            margin-right: 10px;
        }
    </style>
</body>

</html>
