<?php
// Add sample news data to database
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Sample news data
    $sampleNews = [
        [
            'title' => 'DESCRIPTION OF THE INTERLUDE X30 PROJECT',
            'description' => 'Carefully crafted game aspects. Shop up to B-grade. Buffer with premium buff options. Unique events and features await you in this exciting server.',
            'content' => '<h2>Welcome to L2GVE Interlude x30</h2>
                         <p>Our server offers a unique gaming experience with carefully balanced rates and features:</p>
                         <ul>
                         <li>Experience Rate: x30</li>
                         <li>Shop up to B-grade equipment</li>
                         <li>Premium buffer with extended buff options</li>
                         <li>Custom events and features</li>
                         <li>Active community and support</li>
                         </ul>
                         <p>Join us for an unforgettable Lineage 2 experience!</p>',
            'image' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'type' => 'news'
        ],
        [
            'title' => 'OFFICIAL BETA TESTING - AUGUST 12',
            'description' => 'August 12 starts open beta testing of L2GVE server. Join us for exclusive testing and be among the first to experience our unique features.',
            'content' => '<h2>Beta Testing Phase</h2>
                         <p>We are excited to announce the official beta testing phase starting August 12th!</p>
                         <h3>What to expect:</h3>
                         <ul>
                         <li>Full server functionality testing</li>
                         <li>Exclusive beta rewards</li>
                         <li>Direct feedback to development team</li>
                         <li>Early access to new features</li>
                         </ul>
                         <p>Beta testers will receive special recognition and rewards when the server officially launches.</p>',
            'image' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'type' => 'promotions'
        ],
        [
            'title' => 'NEW CUSTOM FEATURES REVEALED',
            'description' => 'Discover the unique custom features that make L2GVE stand out from other servers. Enhanced gameplay mechanics and quality of life improvements.',
            'content' => '<h2>Custom Features Overview</h2>
                         <p>L2GVE introduces several custom features designed to enhance your gaming experience:</p>
                         <h3>Enhanced Features:</h3>
                         <ul>
                         <li>Custom skill balancing</li>
                         <li>Improved drop rates for rare items</li>
                         <li>Quality of life improvements</li>
                         <li>Custom events and competitions</li>
                         <li>Enhanced PvP mechanics</li>
                         </ul>
                         <p>These features are designed to provide a balanced and enjoyable experience for all players.</p>',
            'image' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'type' => 'features'
        ],
        [
            'title' => 'GRAND OPENING CELEBRATION',
            'description' => 'Join our grand opening celebration with special events, bonuses, and exclusive rewards for early players.',
            'content' => '<h2>Grand Opening Event</h2>
                         <p>Celebrate the launch of L2GVE with our grand opening event!</p>
                         <h3>Event Highlights:</h3>
                         <ul>
                         <li>Double XP weekend</li>
                         <li>Exclusive opening rewards</li>
                         <li>Special drop rate bonuses</li>
                         <li>Community competitions</li>
                         <li>Rare item giveaways</li>
                         </ul>
                         <p>Don\'t miss this opportunity to get ahead in the game!</p>',
            'image' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'type' => 'promotions'
        ],
        [
            'title' => 'SERVER MAINTENANCE SCHEDULE',
            'description' => 'Regular maintenance schedule to ensure optimal server performance and stability. Updates and improvements included.',
            'content' => '<h2>Maintenance Information</h2>
                         <p>To ensure the best gaming experience, we perform regular maintenance:</p>
                         <h3>Maintenance Schedule:</h3>
                         <ul>
                         <li>Weekly maintenance: Every Tuesday 3:00-5:00 AM GMT</li>
                         <li>Monthly updates: First Sunday of each month</li>
                         <li>Emergency maintenance: As needed</li>
                         </ul>
                         <p>During maintenance, we implement bug fixes, balance updates, and new features.</p>',
            'image' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'type' => 'news'
        ],
        [
            'title' => 'ADVANCED PVP SYSTEM',
            'description' => 'Experience our enhanced PvP system with balanced mechanics, rewards, and competitive gameplay features.',
            'content' => '<h2>PvP System Features</h2>
                         <p>Our advanced PvP system offers:</p>
                         <h3>Key Features:</h3>
                         <ul>
                         <li>Balanced class mechanics</li>
                         <li>PvP ranking system</li>
                         <li>Seasonal tournaments</li>
                         <li>Exclusive PvP rewards</li>
                         <li>Anti-cheat protection</li>
                         </ul>
                         <p>Compete with players worldwide in fair and exciting PvP battles!</p>',
            'image' => 'https://l2gve.com/template/site/l2gve/images/post/img-def-0.jpg',
            'type' => 'features'
        ]
    ];
    
    // Clear existing sample data (optional)
    $conn->exec("DELETE FROM news WHERE title LIKE '%DESCRIPTION OF THE INTERLUDE%' OR title LIKE '%OFFICIAL BETA TESTING%'");
    
    // Insert sample news
    $stmt = $conn->prepare("INSERT INTO news (title, description, content, image, type, status) VALUES (?, ?, ?, ?, ?, 'active')");
    
    $inserted = 0;
    foreach ($sampleNews as $news) {
        if ($stmt->execute([
            $news['title'],
            $news['description'],
            $news['content'],
            $news['image'],
            $news['type']
        ])) {
            $inserted++;
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => "Successfully inserted {$inserted} sample news items",
        'inserted_count' => $inserted,
        'total_attempted' => count($sampleNews)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
