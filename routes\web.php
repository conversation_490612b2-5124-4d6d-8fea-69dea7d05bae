<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\FeaturesController;
use App\Http\Controllers\WikiController;
use App\Http\Controllers\DownloadController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\NewsAdminController;

// Default route - redirect to English
Route::get('/', function () {
    return redirect('/en');
});

// Localized routes
Route::group(['prefix' => '{locale}', 'where' => ['locale' => 'en|vn'], 'middleware' => 'setlocale'], function () {
    // Home page
    Route::get('/', [HomeController::class, 'index'])->name('home');

    // News routes
    Route::get('/news', [NewsController::class, 'index'])->name('news.index');
    Route::get('/all-news', [NewsController::class, 'allNews'])->name('news.all');
    Route::get('/news/{id}', [NewsController::class, 'show'])->name('news.show');

    // Features routes
    Route::get('/features', [FeaturesController::class, 'index'])->name('features.index');
    Route::get('/all-features', [FeaturesController::class, 'allFeatures'])->name('features.all');
    Route::get('/features/{id}', [FeaturesController::class, 'show'])->name('features.show');

    // Wiki page
    Route::get('/wiki', [WikiController::class, 'index'])->name('wiki');

    // Download page
    Route::get('/download', [DownloadController::class, 'index'])->name('download');

    // API routes
    Route::get('/api/news', [NewsController::class, 'api'])->name('api.news');
});

// Admin routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminController::class, 'index'])->name('index');
    Route::resource('news', NewsAdminController::class);
});
