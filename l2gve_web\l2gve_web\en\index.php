<?php
declare(strict_types=1);

// Simple redirect to HTML file to avoid PHP issues
if (file_exists(__DIR__ . '/index.html')) {
    // Read and serve the HTML file
    $html_content = file_get_contents(__DIR__ . '/index.html');

    // Add PHP optimizations header
    header('X-Powered-By: L2GVE-PHP8.4-Optimized');
    header('Cache-Control: public, max-age=3600');

    echo $html_content;
    exit;
}

// Fallback: Load the optimized template
require_once __DIR__ . '/../template/optimized_index.php';
echo renderOptimizedIndex('en');
?>
