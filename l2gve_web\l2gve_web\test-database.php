<?php
// Test database connection
require_once 'config/database.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Database Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            color: blue;
            background: #e6f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Database Connection</h1>
        
        <?php
        try {
            echo '<div class="info">🔄 Đang kết nối database...</div>';
            
            $db = Database::getInstance();
            $conn = $db->getConnection();
            
            echo '<div class="success">✅ Kết nối database thành công!</div>';
            
            // Test query to get news
            echo '<div class="info">📰 Đang lấy dữ liệu tin tức...</div>';
            
            $stmt = $conn->query("SELECT id, title, description, type, status, created_at FROM news ORDER BY created_at DESC");
            $news = $stmt->fetchAll();
            
            if (empty($news)) {
                echo '<div class="error">⚠️ Không có tin tức nào trong database</div>';
                echo '<div class="info">💡 Hãy thêm tin tức qua admin panel: <a href="admin/news.php">admin/news.php</a></div>';
            echo '<div class="info">Đường dẫn đầy đủ: <code>http://localhost/l2gve_web/l2gve_web/admin/news.php</code></div>';
            } else {
                echo '<div class="success">✅ Tìm thấy ' . count($news) . ' tin tức</div>';
                
                echo '<h3>Danh sách tin tức:</h3>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Tiêu đề</th><th>Loại</th><th>Trạng thái</th><th>Ngày tạo</th></tr>';
                
                foreach ($news as $item) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($item['id']) . '</td>';
                    echo '<td>' . htmlspecialchars($item['title']) . '</td>';
                    echo '<td>' . htmlspecialchars($item['type']) . '</td>';
                    echo '<td>' . htmlspecialchars($item['status']) . '</td>';
                    echo '<td>' . htmlspecialchars($item['created_at']) . '</td>';
                    echo '</tr>';
                }
                
                echo '</table>';
            }
            
            // Test API endpoint
            echo '<div class="info">🔗 Test API endpoint:</div>';
            echo '<p><a href="api/news.php" target="_blank">api/news.php</a></p>';
            echo '<p><a href="api/test.php" target="_blank">api/test.php</a></p>';
            echo '<p>Đường dẫn đầy đủ: <code>http://localhost/l2gve_web/l2gve_web/api/news.php</code></p>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Lỗi kết nối database: ' . htmlspecialchars($e->getMessage()) . '</div>';
            echo '<div class="info">💡 Kiểm tra:</div>';
            echo '<ul>';
            echo '<li>XAMPP MySQL đã chạy chưa?</li>';
            echo '<li>Database "l2gve_web_db" đã tồn tại chưa?</li>';
            echo '<li>Bảng "news" đã được tạo chưa?</li>';
            echo '<li>Thông tin kết nối trong config/database.php có đúng không?</li>';
            echo '</ul>';
        }
        ?>
        
        <div style="margin-top: 30px;">
            <h3>Liên kết hữu ích:</h3>
            <ul>
                <li><a href="admin/news.php">Quản lý tin tức</a></li>
                <li><a href="test-api.html">Test API</a></li>
                <li><a href="vn/index.html">Trang chính (VN)</a></li>
                <li><a href="en/index.html">Trang chính (EN)</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
