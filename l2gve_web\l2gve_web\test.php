<?php
declare(strict_types=1);

// Test file để kiểm tra PHP 8.4 và các tối ưu hóa
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>L2GVE - PHP 8.4 Test</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container { 
            background: rgba(255,255,255,0.1); 
            backdrop-filter: blur(10px);
            border-radius: 20px; 
            padding: 40px; 
            text-align: center; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
        }
        h1 { font-size: 2.5rem; margin-bottom: 20px; }
        .status { 
            display: inline-block; 
            padding: 10px 20px; 
            border-radius: 25px; 
            margin: 10px; 
            font-weight: bold;
        }
        .success { background: rgba(46, 204, 113, 0.8); }
        .warning { background: rgba(241, 196, 15, 0.8); }
        .error { background: rgba(231, 76, 60, 0.8); }
        .info { background: rgba(52, 152, 219, 0.8); }
        .metric { 
            display: flex; 
            justify-content: space-between; 
            padding: 10px 0; 
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .metric:last-child { border-bottom: none; }
        .links { margin-top: 30px; }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 25px; 
            margin: 5px; 
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }
        .btn:hover { 
            background: rgba(255,255,255,0.3); 
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 L2GVE - PHP 8.4 Ready!</h1>
        
        <div class="status-grid">
            <div class="status <?= version_compare(PHP_VERSION, '8.0.0', '>=') ? 'success' : 'error' ?>">
                PHP <?= PHP_VERSION ?>
            </div>
            
            <div class="status <?= extension_loaded('opcache') ? 'success' : 'warning' ?>">
                OPcache <?= extension_loaded('opcache') ? '✅' : '❌' ?>
            </div>
            
            <div class="status <?= function_exists('opcache_get_status') && opcache_get_status() ? 'success' : 'warning' ?>">
                OPcache Active <?= function_exists('opcache_get_status') && opcache_get_status() ? '✅' : '❌' ?>
            </div>
            
            <div class="status <?= ini_get('opcache.jit_buffer_size') ? 'success' : 'warning' ?>">
                JIT <?= ini_get('opcache.jit_buffer_size') ? '✅ ' . ini_get('opcache.jit_buffer_size') : '❌' ?>
            </div>
        </div>

        <div style="margin: 30px 0;">
            <h3>📊 System Metrics</h3>
            <div class="metric">
                <span>Memory Usage:</span>
                <span><?= round(memory_get_usage(true) / 1024 / 1024, 2) ?> MB</span>
            </div>
            <div class="metric">
                <span>Peak Memory:</span>
                <span><?= round(memory_get_peak_usage(true) / 1024 / 1024, 2) ?> MB</span>
            </div>
            <div class="metric">
                <span>Execution Time:</span>
                <span><?= round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) ?> ms</span>
            </div>
            <?php if (function_exists('opcache_get_status') && opcache_get_status()): ?>
            <?php $opcache = opcache_get_status(); ?>
            <div class="metric">
                <span>OPcache Hit Rate:</span>
                <span><?= round($opcache['opcache_statistics']['opcache_hit_rate'], 2) ?>%</span>
            </div>
            <div class="metric">
                <span>Cached Scripts:</span>
                <span><?= count($opcache['scripts']) ?></span>
            </div>
            <?php endif; ?>
        </div>

        <div class="links">
            <h3>🔗 Quick Links</h3>
            <a href="en/" class="btn">🇺🇸 English Site</a>
            <a href="vn/" class="btn">🇻🇳 Vietnamese Site</a>
            <a href="admin/" class="btn">⚙️ Admin Panel</a>
            <a href="admin/performance_monitor.php" class="btn">📊 Performance Monitor</a>
        </div>

        <div style="margin-top: 30px; font-size: 0.9rem; opacity: 0.8;">
            <p>✨ Optimized with PHP 8.4 JIT Compiler</p>
            <p>🚀 Asset bundling & caching enabled</p>
            <p>⚡ Service Worker for offline support</p>
        </div>
    </div>

    <script>
    // Test JavaScript performance
    console.log('🚀 L2GVE loaded successfully!');
    console.log('📊 Page load time:', performance.now().toFixed(2) + 'ms');
    
    // Test Service Worker
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('sw.js')
            .then(reg => console.log('✅ Service Worker registered'))
            .catch(err => console.log('❌ Service Worker failed:', err));
    }
    </script>
</body>
</html>
