<?php
declare(strict_types=1);

require_once __DIR__ . '/database.php';
require_once __DIR__ . '/cache_manager.php';

class QueryBuilder {
    private Database $db;
    private CacheInterface $cache;
    private string $table = '';
    private array $select = ['*'];
    private array $where = [];
    private array $joins = [];
    private array $orderBy = [];
    private array $groupBy = [];
    private array $having = [];
    private ?int $limit = null;
    private ?int $offset = null;
    private array $bindings = [];
    
    public function __construct(Database $db = null, CacheInterface $cache = null) {
        $this->db = $db ?? Database::getInstance();
        $this->cache = $cache ?? CacheManager::getInstance();
    }
    
    public function table(string $table): self {
        $this->table = $table;
        return $this;
    }
    
    public function select(string|array $columns = ['*']): self {
        $this->select = is_array($columns) ? $columns : [$columns];
        return $this;
    }
    
    public function where(string $column, mixed $operator, mixed $value = null): self {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $placeholder = ':where_' . count($this->where);
        $this->where[] = "{$column} {$operator} {$placeholder}";
        $this->bindings[$placeholder] = $value;
        
        return $this;
    }
    
    public function whereIn(string $column, array $values): self {
        $placeholders = [];
        foreach ($values as $i => $value) {
            $placeholder = ":wherein_{$column}_{$i}";
            $placeholders[] = $placeholder;
            $this->bindings[$placeholder] = $value;
        }
        
        $this->where[] = "{$column} IN (" . implode(', ', $placeholders) . ")";
        return $this;
    }
    
    public function whereBetween(string $column, mixed $min, mixed $max): self {
        $minPlaceholder = ":between_min_" . count($this->where);
        $maxPlaceholder = ":between_max_" . count($this->where);
        
        $this->where[] = "{$column} BETWEEN {$minPlaceholder} AND {$maxPlaceholder}";
        $this->bindings[$minPlaceholder] = $min;
        $this->bindings[$maxPlaceholder] = $max;
        
        return $this;
    }
    
    public function join(string $table, string $first, string $operator, string $second): self {
        $this->joins[] = "INNER JOIN {$table} ON {$first} {$operator} {$second}";
        return $this;
    }
    
    public function leftJoin(string $table, string $first, string $operator, string $second): self {
        $this->joins[] = "LEFT JOIN {$table} ON {$first} {$operator} {$second}";
        return $this;
    }
    
    public function orderBy(string $column, string $direction = 'ASC'): self {
        $this->orderBy[] = "{$column} {$direction}";
        return $this;
    }
    
    public function groupBy(string|array $columns): self {
        $columns = is_array($columns) ? $columns : [$columns];
        $this->groupBy = array_merge($this->groupBy, $columns);
        return $this;
    }
    
    public function having(string $column, mixed $operator, mixed $value = null): self {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $placeholder = ':having_' . count($this->having);
        $this->having[] = "{$column} {$operator} {$placeholder}";
        $this->bindings[$placeholder] = $value;
        
        return $this;
    }
    
    public function limit(int $limit): self {
        $this->limit = $limit;
        return $this;
    }
    
    public function offset(int $offset): self {
        $this->offset = $offset;
        return $this;
    }
    
    public function get(int $cacheTtl = 0): array {
        $sql = $this->buildSelectQuery();
        
        if ($cacheTtl > 0) {
            $cacheKey = 'query_' . md5($sql . serialize($this->bindings));
            $cached = $this->cache->get($cacheKey);
            if ($cached !== null) {
                return json_decode($cached, true);
            }
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($this->bindings);
        $result = $stmt->fetchAll();
        
        if ($cacheTtl > 0) {
            $this->cache->set($cacheKey, json_encode($result), $cacheTtl);
        }
        
        return $result;
    }
    
    public function first(int $cacheTtl = 0): ?array {
        $this->limit(1);
        $results = $this->get($cacheTtl);
        return $results[0] ?? null;
    }
    
    public function count(string $column = '*'): int {
        $originalSelect = $this->select;
        $this->select = ["COUNT({$column}) as count"];
        
        $result = $this->first();
        $this->select = $originalSelect;
        
        return (int)($result['count'] ?? 0);
    }
    
    public function exists(): bool {
        return $this->count() > 0;
    }
    
    public function insert(array $data): string {
        $columns = array_keys($data);
        $placeholders = array_map(fn($col) => ":{$col}", $columns);
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($data);
        
        return $this->db->lastInsertId();
    }
    
    public function insertMany(array $data): bool {
        if (empty($data)) {
            return true;
        }
        
        $columns = array_keys($data[0]);
        $placeholders = '(' . implode(', ', array_map(fn($col) => ":{$col}", $columns)) . ')';
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES ";
        
        $values = [];
        $bindings = [];
        
        foreach ($data as $i => $row) {
            $rowPlaceholders = [];
            foreach ($columns as $col) {
                $placeholder = ":{$col}_{$i}";
                $rowPlaceholders[] = $placeholder;
                $bindings[$placeholder] = $row[$col];
            }
            $values[] = '(' . implode(', ', $rowPlaceholders) . ')';
        }
        
        $sql .= implode(', ', $values);
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($bindings);
    }
    
    public function update(array $data): int {
        $setParts = [];
        foreach ($data as $column => $value) {
            $placeholder = ":set_{$column}";
            $setParts[] = "{$column} = {$placeholder}";
            $this->bindings[$placeholder] = $value;
        }
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $setParts);
        
        if (!empty($this->where)) {
            $sql .= " WHERE " . implode(' AND ', $this->where);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($this->bindings);
        
        return $stmt->rowCount();
    }
    
    public function delete(): int {
        $sql = "DELETE FROM {$this->table}";
        
        if (!empty($this->where)) {
            $sql .= " WHERE " . implode(' AND ', $this->where);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($this->bindings);
        
        return $stmt->rowCount();
    }
    
    private function buildSelectQuery(): string {
        $sql = "SELECT " . implode(', ', $this->select) . " FROM {$this->table}";
        
        if (!empty($this->joins)) {
            $sql .= " " . implode(' ', $this->joins);
        }
        
        if (!empty($this->where)) {
            $sql .= " WHERE " . implode(' AND ', $this->where);
        }
        
        if (!empty($this->groupBy)) {
            $sql .= " GROUP BY " . implode(', ', $this->groupBy);
        }
        
        if (!empty($this->having)) {
            $sql .= " HAVING " . implode(' AND ', $this->having);
        }
        
        if (!empty($this->orderBy)) {
            $sql .= " ORDER BY " . implode(', ', $this->orderBy);
        }
        
        if ($this->limit !== null) {
            $sql .= " LIMIT {$this->limit}";
        }
        
        if ($this->offset !== null) {
            $sql .= " OFFSET {$this->offset}";
        }
        
        return $sql;
    }
    
    public function toSql(): string {
        return $this->buildSelectQuery();
    }
    
    public function getBindings(): array {
        return $this->bindings;
    }
    
    // Reset builder for reuse
    public function reset(): self {
        $this->table = '';
        $this->select = ['*'];
        $this->where = [];
        $this->joins = [];
        $this->orderBy = [];
        $this->groupBy = [];
        $this->having = [];
        $this->limit = null;
        $this->offset = null;
        $this->bindings = [];
        
        return $this;
    }
}
