# Cài đặt HTTPS cho XAMPP

## Cách 1: Sử dụng XAMPP SSL (Khuyến nghị cho development)

1. **Mở XAMPP Control Panel**
2. **Click Config bên cạnh Apache**
3. **Chọn "httpd-ssl.conf"**
4. **Tìm và sửa:**
   ```
   DocumentRoot "C:/xampp/htdocs/"
   ServerName localhost:443
   ```

5. **Trong XAMPP Control Panel, click Config > Apache > httpd.conf**
6. **Uncomment dòng:**
   ```
   Include conf/extra/httpd-ssl.conf
   LoadModule ssl_module modules/mod_ssl.so
   ```

7. **Restart Apache**
8. **Truy cập:** `https://localhost/l2gve_web/l2gve_web/`

## Cách 2: Chỉ dùng HTTP cho development
HTTP hoàn toàn OK cho development local. Chỉ cần HTTPS khi deploy production.

## Lưu ý
- Browser sẽ cảnh báo "Not Secure" với self-signed certificate
- Click "Advanced" > "Proceed to localhost" để tiếp tục
- Hoặc cứ dùng HTTP cho development, deploy production mới cần HTTPS
