# Enable rewrite engine
RewriteEngine On

# Redirect root to default language with full path
RewriteRule ^l2gve_web/l2gve_web/?$ /l2gve_web/l2gve_web/en/index [R=302,L]

# Redirect language root to index
RewriteRule ^l2gve_web/l2gve_web/(en|vn)/?$ /l2gve_web/l2gve_web/$1/index [R=302,L]

# Redirect .html URLs to clean URLs
RewriteCond %{THE_REQUEST} \s/l2gve_web/l2gve_web/(en|vn)/([^.\s?]+)\.html [NC]
RewriteRule ^ /l2gve_web/l2gve_web/%1/%2? [NC,L,R=302]

# Serve index.html for index URLs
RewriteRule ^l2gve_web/l2gve_web/(en|vn)/index/?$ /l2gve_web/l2gve_web/$1/index.html [NC,L]

# Remove .html extension from other URLs
RewriteCond %{REQUEST_URI} !^/api/
RewriteCond %{DOCUMENT_ROOT}/l2gve_web/l2gve_web/$1/$2.html -f
RewriteRule ^l2gve_web/l2gve_web/(en|vn)/([^\.]+)/?$ /l2gve_web/l2gve_web/$1/$2.html [NC,L]

# Allow API access
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} ^/api/
RewriteRule ^api/(.*)$ api/$1 [L]

# Enable CORS for API
<FilesMatch "\.(php)$">
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</FilesMatch>

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
