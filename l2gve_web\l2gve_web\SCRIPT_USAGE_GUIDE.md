# Script Usage Guide - L2GVE Web

## 📜 **1. Clean App.js - Thay thế script bị obfuscated**

### Sử dụng:
```html
<!-- Thay thế script cũ -->
<script src="../template/site/l2gve/js/clean-app.js"></script>
```

### Tính năng:
- ✅ Tab functionality (gwOpenTab, gwTabHide)
- ✅ Section handler cho smooth scrolling
- ✅ Place elements responsive
- ✅ Animating numbers
- ✅ Không bị obfuscated, d<PERSON> customize

### Ví dụ sử dụng:
```javascript
// Mở tab
gwOpenTab(['tab1', 'tab2']);

// Ẩn group tabs
gwTabHide(['group1', 'group2']);

// Animate số
AnimatingNumbers(element, 0, 100, 2000);
```

---

## ⏰ **2. Countdown Timer - Time Until OBT**

### Sử dụng cơ bản:
```html
<!-- HTML -->
<div data-countdown class="countdown-container"></div>

<!-- JavaScript -->
<script src="../template/site/l2gve/js/countdown-timer.js"></script>
<script>
// Countdown đến ngày 1/9/2025 12:00 GMT+7
const countdown = new CountdownTimer({
    year: 2025,
    month: 9,
    day: 1,
    hour: 12,
    minute: 0,
    timeZone: 7
});
</script>
```

### Tùy chỉnh nâng cao:
```javascript
const countdown = new CountdownTimer({
    // Target date
    year: 2025,
    month: 12,
    day: 25,
    hour: 18,
    minute: 30,
    timeZone: 7, // GMT+7
    
    // Display format
    format: 'DHMS', // 'DHMS', 'HMS', 'COMPACT'
    container: '[data-countdown]',
    
    // Messages
    endTimeMSG: 'Server is now LIVE! 🎉',
    labels: {
        days: 'Ngày',
        hours: 'Giờ',
        minutes: 'Phút',
        seconds: 'Giây'
    },
    
    // Callbacks
    onTick: (timeLeft) => {
        console.log(`${timeLeft.days}d ${timeLeft.hours}h left`);
    },
    onComplete: () => {
        alert('Server đã mở!');
        window.location.reload();
    }
});
```

### Các format hiển thị:
- **DHMS**: `02 Days : 15 Hours : 30 Minutes : 45 Seconds`
- **HMS**: `63 Hours : 30 Minutes : 45 Seconds`
- **COMPACT**: `2d 15:30:45`

### Methods:
```javascript
countdown.start();           // Bắt đầu
countdown.stop();            // Dừng
countdown.restart();         // Khởi động lại
countdown.setTarget(2025, 12, 31, 23, 59, 59); // Đổi target
countdown.getTimeLeft();     // Lấy thời gian còn lại
```

---

## 📝 **3. Rich Text Editor - Admin News**

### Tính năng:
- ✅ **Visual Editor**: WYSIWYG với Quill.js
- ✅ **HTML Editor**: Chỉnh sửa HTML trực tiếp
- ✅ **Preview**: Xem trước kết quả
- ✅ **Sync**: Đồng bộ giữa các editor

### Toolbar features:
- Headers (H1, H2, H3)
- Bold, Italic, Underline, Strike
- Text & Background Colors
- Lists (Ordered, Bullet)
- Text Alignment
- Links, Images, Videos
- Blockquotes, Code blocks
- Clean formatting

### Sử dụng:
1. **Visual Tab**: Viết như Word
2. **HTML Tab**: Chỉnh sửa HTML code
3. **Preview Tab**: Xem kết quả cuối cùng

### Tích hợp vào form khác:
```html
<!-- Include Quill -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

<!-- HTML structure -->
<div class="editor-container">
    <div class="editor-tabs">
        <div class="editor-tab active" onclick="switchEditorTab('visual')">Visual</div>
        <div class="editor-tab" onclick="switchEditorTab('html')">HTML</div>
        <div class="editor-tab" onclick="switchEditorTab('preview')">Preview</div>
    </div>
    
    <div id="visual-panel" class="editor-panel active">
        <div id="editor"></div>
    </div>
    
    <div id="html-panel" class="editor-panel">
        <textarea id="html-editor" class="html-editor"></textarea>
    </div>
    
    <div id="preview-panel" class="editor-panel">
        <div id="preview-content"></div>
    </div>
    
    <textarea id="content" style="display: none;"></textarea>
</div>
```

---

## 🔧 **4. Cách thay thế script cũ**

### Trong index.html:
```html
<!-- Thay thế -->
<script src="../template/site/l2gve/js/app.js@v=1715210938"></script>

<!-- Bằng -->
<script src="../template/site/l2gve/js/clean-app.js"></script>
<script src="../template/site/l2gve/js/countdown-timer.js"></script>
```

### Khởi tạo countdown:
```html
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Countdown đến OBT
    const obtCountdown = new CountdownTimer({
        year: 2025,
        month: 9,
        day: 1,
        hour: 12,
        timeZone: 7,
        container: '[data-countdown]',
        endTimeMSG: 'Open Beta Test is LIVE! 🎮',
        labels: {
            days: 'Ngày',
            hours: 'Giờ', 
            minutes: 'Phút',
            seconds: 'Giây'
        }
    });
});
</script>
```

---

## 🎯 **5. Lợi ích của script mới**

### ✅ **Clean App.js**:
- Không bị obfuscated
- Dễ debug và customize
- Performance tốt hơn
- Tương thích với modern browsers

### ✅ **Countdown Timer**:
- Highly customizable
- Multiple display formats
- Timezone support
- Event callbacks
- Auto CSS styling

### ✅ **Rich Text Editor**:
- Professional editing experience
- Multiple editing modes
- Real-time preview
- HTML export
- Mobile friendly

---

## 📱 **6. Test Links**

- **Admin với Rich Editor**: `http://localhost/l2gve_web/l2gve_web/admin/news.php`
- **Trang chính**: `http://localhost/l2gve_web/l2gve_web/vn/index.html`
- **Test Scripts**: Mở Developer Console để xem logs
