<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\News;

class FeaturesController extends Controller
{
    public function index(Request $request)
    {
        $locale = app()->getLocale();

        // Get paginated features from news table with type = 'features'
        $features = News::published()
            ->language($locale)
            ->where('type', 'features')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('features.index', compact('features', 'locale'));
    }

    public function allFeatures(Request $request)
    {
        $locale = app()->getLocale();

        // Get all features for the current language
        $features = News::published()
            ->language($locale)
            ->where('type', 'features')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('features.all', compact('features', 'locale'));
    }

    public function show(Request $request, $locale, $id)
    {
        $feature = News::published()
            ->language($locale)
            ->where('type', 'features')
            ->findOrFail($id);

        // Get related features
        $relatedFeatures = News::published()
            ->language($locale)
            ->where('type', 'features')
            ->where('id', '!=', $id)
            ->take(3)
            ->get();

        return view('features.show', compact('feature', 'relatedFeatures', 'locale'));
    }
}
