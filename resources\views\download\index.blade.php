@extends('layouts.app')

@section('title', __('messages.Download') . ' - L2GVE')
@section('description', 'Download L2GVE Lineage 2 client and start your adventure')

@section('content')
<section class="section" data-section="download" data-target-section="download" data-target-section-offset="0" id="download">
    <div class="container" data-container="download">
        <div class="downloads" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="downloads__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h1 class="heading__title">{{ __('messages.Download') }}</h1>
                <div class="heading__dec">
                    <img src="{{ asset('template/site/l2gve/images/heading/dec.png') }}" alt="decoration" class="heading__dec-img">
                </div>
            </div>
            
            <div class="downloads__content">
                <div class="downloads__list">
                    <!-- 1. Download Client -->
                    <div class="downloads__box">
                        <h2 class="downloads__title">{{ __('messages.Game Client') }}</h2>
                        <p class="downloads__desc">{{ __('messages.Download the complete L2GVE game client') }}</p>
                        <div class="downloads__btns">
                            <a href="#" class="button" onclick="alert('Download link will be available soon!')">
                                <i class="gwi gwi_download"></i>
                                {{ __('messages.Download Client') }}
                            </a>
                        </div>
                        <div class="downloads__info">
                            <p><strong>{{ __('messages.File Size') }}:</strong> ~4.5 GB</p>
                            <p><strong>{{ __('messages.Version') }}:</strong> Essence GvE x100</p>
                        </div>
                    </div>

                    <!-- 2. Download System -->
                    <div class="downloads__box">
                        <h2 class="downloads__title">{{ __('messages.System Files') }}</h2>
                        <p class="downloads__desc">{{ __('messages.Download essential system files and configurations') }}</p>
                        <div class="downloads__btns">
                            <a href="#" class="button" onclick="alert('System files download will be available soon!')">
                                <i class="gwi gwi_settings"></i>
                                {{ __('messages.Download System') }}
                            </a>
                        </div>
                        <div class="downloads__info">
                            <p><strong>{{ __('messages.File Size') }}:</strong> ~50 MB</p>
                            <p><strong>{{ __('messages.Version') }}:</strong> v1.0</p>
                        </div>
                    </div>

                    <!-- 3. Download Updater -->
                    <div class="downloads__box">
                        <h2 class="downloads__title">{{ __('messages.Game Updater') }}</h2>
                        <p class="downloads__desc">{{ __('messages.Download the automatic game updater tool') }}</p>
                        <div class="downloads__btns">
                            <a href="#" class="button" onclick="alert('Updater download will be available soon!')">
                                <i class="gwi gwi_update"></i>
                                {{ __('messages.Download Updater') }}
                            </a>
                        </div>
                        <div class="downloads__info">
                            <p><strong>{{ __('messages.File Size') }}:</strong> ~25 MB</p>
                            <p><strong>{{ __('messages.Last Updated') }}:</strong> {{ date('M d, Y') }}</p>
                        </div>
                    </div>
                    
                    
                    <!-- System Requirements -->
                    <div class="downloads__box">
                        <h2 class="downloads__title">{{ __('messages.System Requirements') }}</h2>
                        <div class="downloads__requirements">
                            <div class="requirements__section">
                                <h3>{{ __('messages.Minimum Requirements') }}</h3>
                                <ul>
                                    <li><strong>OS:</strong> Windows 7/8/10/11</li>
                                    <li><strong>CPU:</strong> Intel Core 2 Duo / AMD Athlon 64 X2</li>
                                    <li><strong>RAM:</strong> 2 GB</li>
                                    <li><strong>GPU:</strong> DirectX 9.0c compatible</li>
                                    <li><strong>Storage:</strong> 5 GB available space</li>
                                </ul>
                            </div>
                            <div class="requirements__section">
                                <h3>{{ __('messages.Recommended Requirements') }}</h3>
                                <ul>
                                    <li><strong>OS:</strong> Windows 10/11</li>
                                    <li><strong>CPU:</strong> Intel Core i5 / AMD Ryzen 5</li>
                                    <li><strong>RAM:</strong> 8 GB</li>
                                    <li><strong>GPU:</strong> DirectX 11 compatible</li>
                                    <li><strong>Storage:</strong> 10 GB available space (SSD recommended)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Installation Guide -->
                    <div class="downloads__box">
                        <h2 class="downloads__title">{{ __('messages.Installation Guide') }}</h2>
                        <div class="downloads__guide">
                            <ol>
                                <li>{{ __('messages.Download the game client from the link above') }}</li>
                                <li>{{ __('messages.Extract the downloaded archive to your desired location') }}</li>
                                <li>{{ __('messages.Run the game launcher as administrator') }}</li>
                                <li>{{ __('messages.Create your account or login with existing credentials') }}</li>
                                <li>{{ __('messages.Start playing and enjoy the adventure!') }}</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <!-- Discord Invite -->
                <div class="downloads__invite">
                    <a href="#discord" class="button">
                        <i class="gwi gwi_discord"></i>
                        {{ __('messages.Join Discord for Support') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
// Download page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Add download tracking or other functionality here
});
</script>
@endsection
