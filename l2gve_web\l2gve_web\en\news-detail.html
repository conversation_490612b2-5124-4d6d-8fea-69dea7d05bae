<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News Detail - L2GVE</title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="../template/site/l2gve/images/favicon/favicon.ico@v=1" type="image/x-icon">

    <!-- fonts -->
    <link rel="stylesheet" href="../template/site/l2gve/fonts/beaufortforlol/fonts.css" />
    <link rel="stylesheet" href="../template/site/l2gve/fonts/intro/stylesheet.css" />

    <!-- libs -->
    <link rel="stylesheet" href="../template/site/l2gve/libs/gwi/css/gwi.css" />
    <link rel="stylesheet" href="../template/site/l2gve/libs/fancybox/css/jquery.fancybox.min.css" />

    <!-- CSS links -->
    <link rel="stylesheet" href="../template/site/l2gve/css/main.css@v=1719174774.css">
    <link rel="stylesheet" href="../template/site/l2gve/css/custom.css@v=1714752167.css">

    <style>
        /* Language navigation enhancement */
        .lang__current .lang__name {
            font-weight: bold !important;
            color: #ffffff !important;
        }

        .lang__link .lang__name {
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .lang__link:hover .lang__name {
            opacity: 1;
        }
    </style>
</head>
<body class="body">
    <div class="bg">
        <img src="../template/site/l2gve/images/bg/bg-repeat.jpg" alt="bg" class="bg__img" aria-hidden="true" />
    </div>

    <!-- Navigation -->
    <div class="section compensate-for-scrollbar" data-section="navigation">
    <div class="container" data-container="navigation">
        <div class="navigation">
            <div class="navigation__box navigation__box_side_left">
                <a href="index" class="logo">
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img" style="width: 9rem" />
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img logo__img_hover" />
                </a>

            </div>

           <div class="navigation__menu menu" data-menu>
    <div class="menu__content">
        <ul class="menu__list">
            <li class="menu__el">
                <a href="index" class="menu__item" data-menu-close>
                    Home
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="all-news" class="menu__item" data-menu-close>
                    News
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="features" class="menu__item" data-menu-close>
                    Features
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="download" class="menu__item" data-menu-close>
                    Downloads
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="#discord" class="menu__item" data-menu-close>
                    Discord
                </a>
            </li>
            <li class="menu__el menu__el_continer menu__el_desktop_none" data-place-from="scl">
                <div class="scl" data-place-container="scl">
                    <div class="scl__list">
                        <a href="index.html#Telegram" target="_blank" class="scl__item">
                            <i class="gwi gwi_c-telegram scl__ico-c-telegram"></i>
                        </a>
                        <a href="index.html#Discord" target="_blank" class="scl__item">
                            <i class="gwi gwi_discord scl__ico-discord"></i>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>            <div class="navigation__box navigation__box_side_right" data-place-to="auth">
                <div class="navigation__lang lang notranslate">
    <div class="lang__current">
        <div class="lang__name" data-current-lang>en</div>
    </div>
    <div class="lang__list">
        <a href="../vn/news-detail" class="lang__link lang__link_sub" data-google-lang="vi">
            <div class="lang__name">vn</div>
        </a>
        <a href="news-detail" class="lang__link lang__link_sub" data-google-lang="en">
            <div class="lang__name">en</div>
        </a>
    </div>
</div>



            </div>

            <div class="navigation__gw-burger gw-burger" data-gw-burger>
                <div class="gw-burger__box">
                    <div class="gw-burger__line gw-burger__line_pos_top"></div>
                    <div class="gw-burger__line gw-burger__line_pos_middle"></div>
                    <div class="gw-burger__line gw-burger__line_pos_bottom"></div>
                </div>
            </div>
        </div>
        <!-- END  navigation -->
    </div>
</div>
<!-- END  section -->

    <div class="page">
        <section class="section compensate-for-scrollbar" data-section="news-detail">
            <div class="container">
                <div class="news-detail">
                    <div class="navigation-height-compensate"></div>
                    
                    <!-- Back button - will be set by JavaScript -->
                    <div class="back-to-news-wrap">
                        <a href="all-news" class="back-to-news" id="backButton">
                            <i class="gwi gwi_left"></i> <span id="backButtonText">Back to All News</span>
                        </a>
                    </div>

                    <div class="news-detail__content">
                        <div class="news-detail__image">
                            <img id="newsImage" src="" alt="News Image">
                        </div>

                        <div class="news-detail__info">
                            <div class="news-detail__type" id="newsType"></div>
                            <div class="news-detail__date" id="newsDate"></div>
                        </div>

                        <h1 class="news-detail__title" id="newsTitle"></h1>

                        <div class="news-detail__description" id="newsDescription"></div>

                        <div class="news-detail__content-full" id="newsContent"></div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <style>
        .news-detail {
            padding: 100px 0 50px;
            color: #fff;
        }
        
        .news-detail__image img {
            width: 100%;
            max-width: 800px;
            height: auto;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .news-detail__info {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .news-detail__type {
            background: #007cba;
            color: #fff;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 14px;
        }
        
        .news-detail__date {
            color: #999;
            font-size: 14px;
        }
        
        .news-detail__title {
            font-size: 32px;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .news-detail__description {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
            color: #ddd;
        }
        
        .news-detail__content-full {
            font-size: 16px;
            line-height: 1.8;
            color: #ccc;
        }
        
        .back-to-news-wrap {
            margin-bottom: 30px;
        }

        .back-to-news {
            display: inline-flex;
            align-items: center;
            background: linear-gradient(135deg, #007cba, #0056b3);
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            padding: 12px 24px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .back-to-news:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 124, 186, 0.4);
        }

        .back-to-news i {
            margin-right: 10px;
        }
        
        .gwi_left::before {
            content: "←";
            margin-right: 10px;
        }
    </style>

    <script>
        // Lấy ID tin tức từ URL
        const urlParams = new URLSearchParams(window.location.search);
        const newsId = urlParams.get('id');
        
        if (newsId) {
            // Fetch chi tiết tin tức
            fetch(`../api/news.php?id=${newsId}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.querySelector('.news-detail__content').innerHTML = 
                        '<h2>News not found</h2><p>The requested news article could not be found.</p>';
                    return;
                }
                
                // Hiển thị dữ liệu
                document.getElementById('newsImage').src = data.img || '../template/site/l2gve/images/post/img-def-0.jpg';
                document.getElementById('newsType').textContent = data.type;
                document.getElementById('newsDate').textContent = data.date;
                document.getElementById('newsTitle').textContent = data.title;
                document.getElementById('newsDescription').textContent = data.desc;
                document.getElementById('newsContent').innerHTML = data.content || data.desc;

                // Cập nhật back button dựa trên type
                const backButton = document.getElementById('backButton');
                const backButtonText = document.getElementById('backButtonText');

                if (data.type === 'features') {
                    backButton.href = 'features';
                    backButtonText.textContent = 'Back to Features';
                } else {
                    backButton.href = 'all-news';
                    backButtonText.textContent = 'Back to All News';
                }

                // Cập nhật title trang
                document.title = data.title + ' - L2GVE';
            })
            .catch(error => {
                console.error('Error:', error);
                document.querySelector('.news-detail__content').innerHTML = 
                    '<h2>Error</h2><p>Could not load news article.</p>';
            });
        } else {
            document.querySelector('.news-detail__content').innerHTML = 
                '<h2>No news selected</h2><p>Please select a news article to read.</p>';
        }
    </script>

    <!-- Scripts -->
    <script src="../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js"></script>
    <script src="../template/site/l2gve/js/clean-app.js"></script>
    <script src="../template/site/l2gve/js/custom.js@v=1714752167"></script>
</body>
</html>


