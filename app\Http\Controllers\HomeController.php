<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\News;

class HomeController extends Controller
{
    public function index(Request $request)
    {
        $locale = app()->getLocale();

        // Get latest news for the current language with pagination
        $latestNews = News::published()
            ->language($locale)
            ->orderBy('created_at', 'desc')
            ->paginate(6);

        // Also get static news for fallback (for JavaScript)
        $staticNews = News::published()
            ->language($locale)
            ->orderBy('created_at', 'desc')
            ->take(6)
            ->get();

        return view('home.index', compact('latestNews', 'staticNews', 'locale'));
    }
}
