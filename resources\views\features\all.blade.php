@extends('layouts.app')

@section('title', 'All Features - L2GVE')
@section('description', 'Discover all the amazing features and updates in L2GVE Essence GvE x100 server')

@section('styles')
<style>
    .all-features {
        padding: 100px 0 50px;
        min-height: 100vh;
    }

    .all-features__header {
        text-align: center;
        margin-bottom: 50px;
    }

    .all-features__title {
        font-size: 48px;
        color: #fff;
        margin-bottom: 20px;
        font-family: var(--font-1);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .all-features__filters {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-bottom: 40px;
        flex-wrap: wrap;
    }

    .all-features__content {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 30px;
        margin-bottom: 50px;
    }

    .feature-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #ff6b35, #f7931e);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-8px);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
        border-color: rgba(255, 255, 255, 0.25);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    }

    .feature-card:hover::before {
        opacity: 1;
    }

    .feature-card__image {
        width: 100%;
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    .feature-card__image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .feature-card:hover .feature-card__image img {
        transform: scale(1.05);
    }

    .feature-card__content {
        padding: 20px;
    }

    .feature-card__title {
        background: #007cba;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        text-transform: uppercase;
        font-size: 11px;
        font-weight: 600;
        white-space: nowrap;
        display: inline-block;
        min-width: fit-content;
        width: auto;
        height: auto;
        max-width: none;
        flex: none;
        flex-shrink: 0;
        flex-grow: 0;
        flex-basis: auto;
        line-height: 1.2;
        margin: 10px 0;
    }



    .feature-card__excerpt {
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 15px;
    }

    .feature-card__meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
    }

    .feature-card__type {
        background: var(--color-accent);
        color: #fff;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        text-transform: uppercase;
        font-weight: bold;
    }

    .loading {
        text-align: center;
        padding: 50px;
        color: rgba(255, 255, 255, 0.7);
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 40px;
    }

    .pagination {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .pagination .page-link {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #fff;
        padding: 8px 12px;
        text-decoration: none;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .pagination .page-link:hover,
    .pagination .page-item.active .page-link {
        background: var(--color-accent);
        border-color: var(--color-accent);
    }

    @media (max-width: 768px) {
        .all-features__title {
            font-size: 32px;
        }
        
        .all-features__content {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .all-features__filters {
            gap: 10px;
        }
    }
</style>
@endsection

@section('content')
<!-- Background -->
<div class="bg">
    <img src="{{ asset('template/site/l2gve/images/bg/bg-repeat.jpg') }}" alt="bg" class="bg__img" aria-hidden="true" />
</div>

<div class="page">
    <section class="section compensate-for-scrollbar" data-section="all-features">
        <div class="container">
            <div class="all-features">
                <div class="navigation-height-compensate"></div>
                
                <div class="all-features__header" style="margin-top: -50px;">
                    <!-- Title and filters removed as requested -->
                </div>

                <div class="all-features__content" id="featuresContent">
                    @forelse($features as $item)
                        <div class="feature-card" data-type="{{ $item->type }}" onclick="window.location.href='{{ route('features.show', ['locale' => $locale, 'id' => $item->id]) }}'">
                            <div class="feature-card__image">
                                <img src="{{ $item->image ?: asset('template/site/l2gve/images/post/img-def-0.jpg') }}" alt="{{ $item->title }}" loading="lazy">
                            </div>
                            <div class="feature-card__content">
                                <h3 class="feature-card__title">{{ $item->title }}</h3>
                                <div class="feature-card__meta">
                                    <span class="feature-card__type">{{ ucfirst($item->type) }}</span>
                                    <span class="feature-card__date">{{ $item->created_at->format('M d, Y') }}</span>
                                </div>
                                <p class="feature-card__excerpt">{{ $item->excerpt ?: Str::limit(strip_tags($item->content), 150) }}</p>
                            </div>
                        </div>
                    @empty
                        <div class="loading">
                            <p>{{ __('messages.No features available') }}</p>
                        </div>
                    @endforelse
                </div>

                @if($features->hasPages())
                    <div class="pagination-wrapper">
                        {{ $features->links() }}
                    </div>
                @endif
            </div>
        </div>
    </section>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('[data-filter]');
    const featureCards = document.querySelectorAll('.feature-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filterType = this.getAttribute('data-filter');
            
            // Update button states
            filterButtons.forEach(btn => {
                btn.classList.remove('btn_accent');
                btn.classList.add('btn_accent_no');
            });
            this.classList.remove('btn_accent_no');
            this.classList.add('btn_accent');

            // Filter feature cards
            featureCards.forEach(card => {
                const cardType = card.getAttribute('data-type');
                if (filterType === 'all' || cardType === filterType) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
});
</script>
@endsection
