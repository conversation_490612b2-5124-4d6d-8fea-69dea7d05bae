<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <!-- Preload critical resources -->
    <link rel="preload" href="{{ asset('template/site/l2gve/js/clean-app.js') }}" as="script">
    <link rel="preload" href="{{ asset('template/site/l2gve/js/custom.js@v=1714752167') }}" as="script">
    <link rel="preload" href="{{ asset('template/site/l2gve/libs/jquery/jquery-3.4.1.min.js') }}" as="script">

    <!-- DNS prefetch for external domains -->
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    <link rel="dns-prefetch" href="//mc.yandex.ru">
    <link rel="dns-prefetch" href="//ajax.googleapis.com">

    <meta charset="UTF-8">
    <title>@yield('title', 'L2GVE - M<PERSON>y chủ Essence GvE x100')</title>
    <meta name="Description" content="@yield('description', 'Dive into the world of Lineage II with the L2GVE Essence GvE x100 server')">
    <meta name="Keywords" content="Essence GvE x100">

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="theme-color" content="#0f1416">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="viewport" content="width=device-width">

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('template/site/l2gve/images/favicon/favicon.ico@v=1') }}" type="image/x-icon">
    <link rel="icon" sizes="16x16" href="{{ asset('template/site/l2gve/images/favicon/favicon-16x16.png@v=1') }}" type="image/png">
    <link rel="icon" sizes="32x32" href="{{ asset('template/site/l2gve/images/favicon/favicon-32x32.png@v=1') }}" type="image/png">
    <link rel="apple-touch-icon-precomposed" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-precomposed.png@v=1') }}">
    <link rel="apple-touch-icon" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="57x57" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-57x57.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="60x60" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-60x60.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="72x72" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-72x72.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="76x76" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-76x76.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="114x114" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-114x114.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="120x120" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-120x120.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="144x144" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-144x144.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="152x152" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-152x152.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="167x167" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-167x167.png@v=1') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('template/site/l2gve/images/favicon/apple-touch-icon-180x180.png@v=1') }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request()->url() }}">
    <meta property="og:title" content="@yield('title', 'L2GVE - Máy chủ Essence GvE x100')">
    <meta property="og:image" content="{{ asset('template/site/l2gve/images/sclbnr-en.jpg') }}">
    <meta property="og:description" content="@yield('description', 'Dive into the world of Lineage II with the L2GVE Essence GvE x100 server')">
    <meta property="og:site_name" content="L2GVE Essence GvE x100">
    <meta property="og:locale" content="{{ app()->getLocale() == 'vn' ? 'vi_VN' : 'en_US' }}">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <!-- CSS -->
    <link rel="stylesheet" href="{{ asset('template/site/l2gve/css/main.css@v=1719174774.css') }}">
    <link rel="stylesheet" href="{{ asset('template/site/l2gve/css/custom.css@v=1714752167.css') }}">
    <link rel="stylesheet" href="{{ asset('template/site/l2gve/libs/swiper/css/swiper.min.css') }}">
    <link rel="stylesheet" href="{{ asset('template/site/l2gve/libs/fancybox/css/jquery.fancybox.min.css') }}">
    <link rel="stylesheet" href="{{ asset('template/site/l2gve/libs/gwi/css/gwi.css') }}">
    
    <!-- Fonts -->
    <link rel="stylesheet" href="{{ asset('template/site/l2gve/fonts/beaufortforlol/fonts.css') }}">
    <link rel="stylesheet" href="{{ asset('template/site/l2gve/fonts/intro/stylesheet.css') }}">

    <!-- Google Translate -->
    <script type="text/javascript">
        function googleTranslateElementInit() {
            new google.translate.TranslateElement({
                pageLanguage: '{{ app()->getLocale() }}',
                includedLanguages: 'en,vi',
                layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
                autoDisplay: false
            }, 'google_translate_element');
        }
    </script>
    <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

    @yield('styles')

    <!-- JavaScript Config - Exact copy from original template -->
    <script>
        const __config = {
            gFonts: {
                fonts: ["Open Sans:400,500,600,700:latin,vietnamese"],
                delay: 500,
            },
            preload: {
                /* Minimum display time in seconds */
                minTime: 3,
                /* Maximum display time in seconds */
                maxTime: 10,
                /* Use the load event */
                withOnload: true,
                /* Condition check update rate in seconds */
                timeInterval: 0.5,
            },
            sectionSwitcher: {
                // Включить/отключить переключение секций колесиком
                init: true,
                // Скорость переключения между секциями
                speed: 0.4,
                easeType: "power3.out",
            },
            sliders: {
                news: {
                    init: true,
                    loop: false,
                    autoplay: false,
                    autoplayDelay: 10000,
                },
            },
            gwtraslate: {
                /* Original language */
                lang: "{{ app()->getLocale() }}",

                /* The language we translate into on the first visit*/
                langFirstVisit: '{{ app()->getLocale() }}',
            },
            posts: @json(isset($latestNews) ? $latestNews : []),
            language: "{{ app()->getLocale() }}",
            version: "2.0"
        };
    </script>

    <style>
    /* Critical CSS from original */
    .preload {
        background-color: #0f1416;
        min-width: 320px;
        position: fixed;
        z-index: 500;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        box-sizing: border-box;
    }

    /* Heading alignment fixes */
    .heading {
        text-align: center;
        position: relative;
        margin-bottom: 40px;
    }

    .heading__title {
        position: relative;
        z-index: 2;
        margin: 0 auto;
        display: inline-block;
    }

    .heading__dec {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        pointer-events: none;
    }

    .heading__dec-img {
        display: block;
        max-width: 100%;
        height: auto;
    }

    /* Back to Top Button Styles */
    .back-to-top {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%) translateY(100px);
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
        border: 2px solid #4a5568;
        border-radius: 50%;
        color: #e2e8f0;
        cursor: pointer;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
    }

    .back-to-top:hover {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        border-color: #718096;
        color: #ffffff;
        transform: translateX(-50%) translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
    }

    .back-to-top.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
    }

    .back-to-top svg {
        transition: transform 0.2s ease;
    }

    .back-to-top:hover svg {
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .back-to-top {
            width: 45px;
            height: 45px;
            bottom: 20px;
        }
    }

    /* Language navigation enhancement */
    .lang__current .lang__name {
        font-weight: bold !important;
        color: #ffffff !important;
    }

    .lang__link .lang__name {
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .lang__link:hover .lang__name {
        opacity: 1;
    }

    /* Google Translate Widget Styling */
    #google_translate_element {
        display: inline-block;
    }

    #google_translate_element .goog-te-gadget {
        font-family: var(--font-0) !important;
        font-size: 0.875rem !important;
    }

    #google_translate_element .goog-te-gadget-simple {
        background-color: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 4px !important;
        padding: 8px 12px !important;
        color: #ffffff !important;
    }

    #google_translate_element .goog-te-gadget-simple:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
    }

    /* Server Info Styling */
    .server-info, .server-rates {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }

    .server-info__item, .server-rates__item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .server-info__label, .server-rates__label {
        color: #c4b8aa;
        font-weight: 400;
    }

    .server-info__value, .server-rates__value {
        color: #ffffff;
        font-weight: 700;
    }

    .server-info__value--online {
        color: #3af83a !important;
    }

    .server-info__value--offline {
        color: #ff2727 !important;
    }
    </style>
</head>
<body class="body body_home">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T99S9NM7" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

    <!-- Preloader (commented out in original but structure preserved) -->
    <!-- <div class="preload">
        <div class="preload__progress" data-preload-progress></div>
        <img src="{{ asset('template/site/l2gve/images/preload/preload.png') }}" alt="loading.." class="preload__logo" style="width: 9rem" />
        <div class="preload__items">
            <img src="{{ asset('template/site/l2gve/images/preload/item.png') }}" alt="★" style="height: 1.4rem" />
            <img src="{{ asset('template/site/l2gve/images/preload/item.png') }}" alt="★" style="height: 1.4rem" />
            <img src="{{ asset('template/site/l2gve/images/preload/item.png') }}" alt="★" style="height: 1.4rem" />
        </div>
    </div> -->

    <!-- Background -->
    <div class="bg">
        <img src="{{ asset('template/site/l2gve/images/bg/' . (request()->is('*/') || request()->is('/') ? 'bg-full.jpg' : 'bg-repeat.jpg')) }}" alt="bg" class="bg__img" aria-hidden="true" />
    </div>

    <!-- Navigation Section -->
    <div class="section compensate-for-scrollbar" data-section="navigation">
        <div class="container" data-container="navigation">
            <div class="navigation">
                <div class="navigation__box navigation__box_side_left">
                    <a href="{{ route('home', ['locale' => app()->getLocale()]) }}" class="logo">
                        <img src="{{ asset('template/site/l2gve/images/logo/logo.png') }}" alt="logo" class="logo__img" style="width: 9rem" />
                        <img src="{{ asset('template/site/l2gve/images/logo/logo.png') }}" alt="logo" class="logo__img logo__img_hover" />
                    </a>
                </div>

                <div class="navigation__menu menu" data-menu>
                    <div class="menu__content">
                        <ul class="menu__list">
                            <li class="menu__el">
                                <a href="{{ route('home', ['locale' => app()->getLocale()]) }}" class="menu__item" data-menu-close>
                                    {{ __('messages.Home') }}
                                </a>
                            </li>
                            <li class="menu__dot"></li>
                            <li class="menu__el">
                                <a href="{{ route('news.all', ['locale' => app()->getLocale()]) }}" class="menu__item" data-menu-close>
                                    {{ __('messages.News') }}
                                </a>
                            </li>
                            <li class="menu__dot"></li>
                            <li class="menu__el">
                                <a href="{{ route('features.all', ['locale' => app()->getLocale()]) }}" class="menu__item" data-menu-close>
                                    {{ __('messages.Features') }}
                                </a>
                            </li>
                            <li class="menu__dot"></li>
                            <li class="menu__el">
                                <a href="{{ route('download', ['locale' => app()->getLocale()]) }}" class="menu__item" data-menu-close>
                                    {{ __('messages.Download') }}
                                </a>
                            </li>
                            <li class="menu__dot"></li>
                            <li class="menu__el">
                                <a href="#discord" class="menu__item" data-menu-close>
                                    Discord
                                </a>
                            </li>
                            <li class="menu__el menu__el_continer menu__el_desktop_none" data-place-from="scl">
                                <div class="scl" data-place-container="scl">
                                    <div class="scl__list">
                                        <a href="#Telegram" target="_blank" class="scl__item">
                                            <i class="gwi gwi_c-telegram scl__ico-c-telegram"></i>
                                        </a>
                                        <a href="#Discord" target="_blank" class="scl__item">
                                            <i class="gwi gwi_discord scl__ico-discord"></i>
                                        </a>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="navigation__box navigation__box_side_right" data-place-to="auth">
                    <!-- Language Switcher -->
                    <div class="navigation__lang lang notranslate">
                        <div class="lang__current">
                            <div class="lang__name" data-current-lang>{{ app()->getLocale() == 'en' ? 'en' : 'vn' }}</div>
                        </div>
                        <div class="lang__list">
                            <a href="{{ route(Route::currentRouteName(), array_merge(Route::current()->parameters(), ['locale' => 'vn'])) }}" class="lang__link lang__link_sub" data-google-lang="vi">
                                <div class="lang__name">vn</div>
                            </a>
                            <a href="{{ route(Route::currentRouteName(), array_merge(Route::current()->parameters(), ['locale' => 'en'])) }}" class="lang__link lang__link_sub" data-google-lang="en">
                                <div class="lang__name">en</div>
                            </a>
                        </div>
                    </div>

                    <!-- Mobile Menu Button -->
                    <div class="navigation__gw-burger gw-burger" data-gw-burger>
                        <div class="gw-burger__box">
                            <div class="gw-burger__line gw-burger__line_pos_top"></div>
                            <div class="gw-burger__line gw-burger__line_pos_middle"></div>
                            <div class="gw-burger__line gw-burger__line_pos_bottom"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Back to Top Button -->
    <div class="back-to-top" id="backToTop">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
        </svg>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer__content">
                <div class="footer__copy">
                    <p>&copy; {{ date('Y') }} L2GVE. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="{{ asset('template/site/l2gve/libs/jquery/jquery-3.4.1.min.js') }}"></script>
    <script src="{{ asset('template/site/l2gve/libs/gsap/js/gsap.min.js') }}"></script>
    <script src="{{ asset('template/site/l2gve/libs/gsap/js/ScrollTrigger.min.js') }}"></script>
    <script src="{{ asset('template/site/l2gve/libs/gsap/js/ScrollToPlugin.min.js') }}"></script>
    <script src="{{ asset('template/site/l2gve/libs/swiper/js/swiper.min.js') }}" defer></script>
    <script src="{{ asset('template/site/l2gve/libs/fancybox/js/jquery.fancybox.js') }}" defer></script>
    <script src="{{ asset('template/site/l2gve/libs/insertmedia/js/insertmedia.min.js') }}" async></script>
    <script src="{{ asset('template/site/l2gve/libs/countdown/js/jquery.plugin.min.js') }}" async></script>
    <script src="{{ asset('template/site/l2gve/libs/countdown/js/jquery.countdown.min.js') }}" async></script>
    <script src="{{ asset('template/site/l2gve/js/clean-app.js') }}"></script>
    <script src="{{ asset('template/site/l2gve/js/custom.js@v=1714752167') }}"></script>
    
    @yield('scripts')

    <!-- Back to Top Button Script -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const backToTop = document.getElementById('backToTop');

        function updateBackToTop() {
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        }

        window.addEventListener('scroll', updateBackToTop);

        backToTop.addEventListener('click', function() {
            const start = window.pageYOffset;
            const startTime = performance.now();
            const duration = 800;

            function animateScroll(currentTime) {
                const timeElapsed = currentTime - startTime;
                const progress = Math.min(timeElapsed / duration, 1);

                const easeInOutCubic = progress => progress < 0.5
                    ? 4 * progress * progress * progress
                    : 1 - Math.pow(-2 * progress + 2, 3) / 2;

                window.scrollTo(0, start * (1 - easeInOutCubic(progress)));

                if (progress < 1) {
                    requestAnimationFrame(animateScroll);
                }
            }

            requestAnimationFrame(animateScroll);
        });

        updateBackToTop();
    });
    </script>
</body>
</html>
