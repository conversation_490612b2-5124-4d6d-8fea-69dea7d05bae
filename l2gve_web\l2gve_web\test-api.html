<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API News</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .news-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
        }
        .info {
            color: blue;
            background: #e6f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test API News</h1>

        <div class="info">
            <p><strong>Đường dẫn project:</strong> <code>http://localhost/l2gve_web/l2gve_web/</code></p>
            <p><strong>API endpoint:</strong> <code>http://localhost/l2gve_web/l2gve_web/api/news.php</code></p>
        </div>

        <div>
            <button onclick="testAPI()">Test API</button>
            <button onclick="clearResults()">Clear</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Đang test API...</p>';

            // Test multiple API paths
            const apiPaths = [
                './api/news.php',
                '/l2gve_web/api/news.php',
                'api/news.php'
            ];

            for (let i = 0; i < apiPaths.length; i++) {
                const apiPath = apiPaths[i];
                try {
                    console.log(`Testing API path ${i + 1}: ${apiPath}`);

                    const response = await fetch(apiPath, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json'
                        }
                    });

                    console.log(`Path ${i + 1} - Response status:`, response.status);

                    if (response.ok) {
                        const data = await response.json();
                        console.log(`Path ${i + 1} - Response data:`, data);
                
                        if (Array.isArray(data) && data.length > 0) {
                            let html = `<div class="success">✅ API hoạt động tốt! (Đường dẫn: ${apiPath})</div>`;
                            html += `<p><strong>Tìm thấy ${data.length} tin tức:</strong></p>`;

                            data.forEach(item => {
                                html += `
                                    <div class="news-item">
                                        <h3>${item.title}</h3>
                                        <p><strong>Loại:</strong> ${item.type}</p>
                                        <p><strong>Ngày:</strong> ${item.date}</p>
                                        <p>${item.desc || item.description}</p>
                                        ${item.img ? `<p><strong>Hình:</strong> <a href="${item.img}" target="_blank">Xem ảnh</a></p>` : ''}
                                    </div>
                                `;
                            });

                            resultsDiv.innerHTML = html;
                            return; // Success, exit the loop
                        } else {
                            console.log(`Path ${i + 1} - Empty data`);
                        }
                    } else {
                        console.log(`Path ${i + 1} - HTTP ${response.status}: ${response.statusText}`);
                    }

                } catch (error) {
                    console.error(`Path ${i + 1} - Error:`, error);
                }
            }

            // If we get here, all paths failed
            resultsDiv.innerHTML = `
                <div class="error">
                    ❌ Tất cả đường dẫn API đều thất bại!
                    <br><br>
                    <strong>Đã thử:</strong>
                    <ul>
                        ${apiPaths.map(path => `<li>${path}</li>`).join('')}
                    </ul>
                    <br>
                    <strong>Giải pháp:</strong> Kiểm tra console để xem lỗi chi tiết
                </div>
            `;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Auto test when page loads
        window.addEventListener('load', function() {
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
