@extends('layouts.app')

@section('title', 'L2GVE - Lineage 2 Global Veteran Edition')
@section('description', 'Experience the ultimate Lineage 2 private server with enhanced features, balanced gameplay, and active community.')

@section('content')
<!-- Background -->
<div class="bg">
    <img src="{{ asset('template/site/l2gve/images/bg/bg-full.jpg') }}" alt="bg" class="bg__img" aria-hidden="true" />
</div>

<!-- Header Section -->
<header class="section" data-section="header" data-target-section="header" data-target-section-offset="0" id="header">
    <div class="header-scl-box" data-place-to="scl" data-gw-anime="fadeInRight" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s"></div>
    <div class="container" data-container="header">
        <div class="header">
            <div class="header__box">
                <div class="header__content" data-gw-anime="fadeInUp" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                    <div class="header__title">{{ __('messages.Essence GvE 100 - August 16!') }}</div>
                    <div class="header__desc">
                        {{ __('messages.The Essence GvE server complex presents a new project with x100 rates and a unique concept in the world of Lineage 2') }}
                    </div>
                </div>
                <div class="header__btns" data-gw-anime="fadeInDown" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                    <a href="{{ route('download', ['locale' => $locale]) }}" class="button">{{ __('messages.Download') }}</a>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- News Section -->
<section class="section" data-section="news" data-target-section="news" data-target-section-offset="0" id="news">
    <div class="container" data-container="news">
        <div class="news" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="rating__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h2 class="heading__title">{{ __('messages.Infomations') }}</h2>
                <div class="heading__desc">{{ __('messages.Server information') }}</div>
            </div>
            <div class="news__box" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <div class="news__control">
                    <div class="news__btns">
                        <a href="{{ route('news.all', ['locale' => $locale]) }}" class="btn btn_size_small btn_accent_no">
                            {{ __('messages.All') }}
                        </a>
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="news">
                            {{ __('messages.News') }}
                        </a>
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="promotions">
                            {{ __('messages.Promotions') }}
                        </a>
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="features">
                            {{ __('messages.Features') }}
                        </a>
                    </div>
                    <div class="news__arrows arrows">
                        <div class="news__arrow news__arrow_prev arrow arrow_prev" data-slider-prev="news"></div>
                        <div class="news__arrow news__arrow_next arrow arrow_next" data-slider-next="news"></div>
                    </div>
                </div>

                <div class="news__wrap">
                    <div class="news__list" data-slider="news">
                        <div class="swiper-wrapper" data-news-list></div>
                    </div>
                </div>

                <!-- Card View for Filtered Content -->
                <div class="news__cards" id="newsCards" style="display: none;">
                    <div class="news-cards-grid">
                        <!-- Cards will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Pagination -->
                @if($latestNews->hasPages())
                    <div class="news__pagination" style="margin-top: 40px; text-align: center;">
                        {{ $latestNews->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>



<!-- Community Section -->
<section class="section" data-section="community" data-target-section="community" data-target-section-offset="0" id="community">
    <div class="container" data-container="community">
        <div class="community" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="community__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h2 class="heading__title">{{ __('Community') }}</h2>
                <div class="heading__dec">
                    <img src="{{ asset('template/site/l2gve/images/heading/dec.png') }}" alt="decoration" class="heading__dec-img">
                </div>
            </div>
            
            <div class="community__content">
                <!-- Discord Widget -->
                <div class="discord-widget">
                    <div class="discord-fallback" id="discordFallback">
                        <div class="discord-server-info">
                            <h3>L2GVE Discord Server</h3>
                            <p>Join our community for the latest updates, events, and discussions!</p>
                            <div class="discord-stats">
                                <div class="stat-item">
                                    <span class="stat-number" id="memberCount">1,200+</span>
                                    <span class="stat-label">Members</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number" id="onlineCount">150+</span>
                                    <span class="stat-label">Online</span>
                                </div>
                            </div>
                            <a href="https://discord.gg/l2gve" target="_blank" class="discord-join-btn">
                                Join Discord Server
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Alternative Discord Invite Button -->
                <div class="discord-invite" style="text-align: center; margin-top: 20px;">
                    <a href="https://discord.gg/l2gve" target="_blank" class="btn btn_accent">
                        <i class="fab fa-discord" style="margin-right: 8px;"></i>
                        {{ __('messages.Join Discord Server') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('styles')
<style>
.discord-widget {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.discord-widget iframe {
    border-radius: 8px;
    min-height: 400px;
}

.discord-invite .btn {
    padding: 12px 24px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.discord-invite .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(114, 137, 218, 0.3);
}

.discord-fallback {
    background: linear-gradient(135deg, #7289da 0%, #5865f2 100%);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    color: white;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 350px;
    height: 500px;
    box-sizing: border-box;
}

.discord-server-info h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: white;
}

.discord-server-info p {
    font-size: 16px;
    margin-bottom: 25px;
    opacity: 0.9;
}

.discord-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 28px;
    font-weight: bold;
    color: white;
}

.stat-label {
    display: block;
    font-size: 14px;
    opacity: 0.8;
    margin-top: 5px;
}

.discord-join-btn {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    margin-top: 20px;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.discord-join-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.news__pagination .pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.news__pagination .page-item {
    margin: 0;
}

.news__pagination .page-link {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 8px 12px;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.news__pagination .page-link:hover,
.news__pagination .page-item.active .page-link {
    background: var(--color-accent);
    border-color: var(--color-accent);
    color: #fff;
}

.news__pagination .page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
}

.news-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.news-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.news-card__image {
    width: 100%;
    height: 180px;
    overflow: hidden;
}

.news-card__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-card__image img {
    transform: scale(1.05);
}

.news-card__content {
    padding: 20px;
}

.news-card__title {
    background: #007cba;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    text-transform: uppercase;
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
    display: inline-block;
    min-width: fit-content;
    width: auto;
    height: auto;
    max-width: none;
    flex: none;
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: auto;
    line-height: 1.2;
    margin: 10px 0;
}

.news-card__excerpt {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 15px;
}

.news-card__meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.news-card__type {
    background: linear-gradient(135deg, var(--color-accent), #0099cc);
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 700;
}

.news-card__date {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}

.news-card__button {
    background: var(--color-accent);
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.news-card__button:hover {
    background: #005a87;
    transform: translateY(-1px);
}
</style>
@endsection

@section('scripts')
<script>
// Enhanced news loading with better error handling and performance - Exact copy from original template
(function() {
    let newsCache = null;
    let newsLoading = false;

    async function loadNews(type = 'all', limit = 10) {
        if (newsLoading) return;

        const cacheKey = `news_${type}_${limit}`;

        // Check cache first (5 minutes cache)
        if (newsCache && newsCache.key === cacheKey &&
            Date.now() - newsCache.timestamp < 5 * 60 * 1000) {
            __config.posts = newsCache.data;
            updateNewsDisplay();
            return;
        }

        newsLoading = true;

        try {
            const url = `{{ route('api.news', ['locale' => $locale]) }}?limit=${limit}${type !== 'all' ? `&type=${type}` : ''}`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (Array.isArray(data) && data.length > 0) {
                // Cache the result
                newsCache = {
                    key: cacheKey,
                    data: data,
                    timestamp: Date.now()
                };

                __config.posts = data;

                // Trigger news display update
                if (typeof updateNewsDisplay === 'function') {
                    updateNewsDisplay();
                } else if (typeof newsInit === 'function') {
                    newsInit();
                }

                // Dispatch custom event for other components
                document.dispatchEvent(new CustomEvent('newsLoaded', {
                    detail: { news: data, type: type }
                }));

            } else {
                // Use fallback data if no news from API
                throw new Error('No news data available');
            }

        } catch (error) {
            console.error('Error loading news:', error);

            // Fallback data with sample news from Laravel
            const fallbackNews = @json($staticNews);

            __config.posts = fallbackNews;

            if (typeof updateNewsDisplay === 'function') {
                updateNewsDisplay();
            } else if (typeof newsInit === 'function') {
                newsInit();
            }
        } finally {
            newsLoading = false;
        }
    }

    // Function to update news display in slider
    function updateNewsDisplay() {
        const newsContainer = document.querySelector('[data-news-list]');
        if (!newsContainer || !__config.posts) return;

        // Clear existing content
        newsContainer.innerHTML = '';

        // Add news items to slider
        __config.posts.forEach(news => {
            const newsItem = document.createElement('div');
            newsItem.className = 'swiper-slide';
            newsItem.innerHTML = `
                <div class="news__item" data-type="${news.type}">
                    <div class="news__img">
                        <img src="${news.img}" alt="${news.title}" loading="lazy">
                    </div>
                    <div class="news__content">
                        <div class="news__title">${news.title}</div>
                        <div class="news__desc">${news.desc}</div>
                        <div class="news__date">${formatDate(news.date)}</div>
                    </div>
                    <a href="${news.url}" class="news__link"></a>
                </div>
            `;
            newsContainer.appendChild(newsItem);
        });

        // Reinitialize Swiper if available
        if (window.Swiper && window.newsSlider) {
            window.newsSlider.update();
            window.newsSlider.slideTo(0);
        }
    }

    // Function to display news as cards
    function displayNewsCards(filterType = null, limit = null) {
        const cardsContainer = document.getElementById('newsCards');
        const sliderContainer = document.querySelector('.news__list');
        const cardsGrid = cardsContainer.querySelector('.news-cards-grid');

        if (!cardsContainer || !cardsGrid) return;

        // Show cards, hide slider
        cardsContainer.style.display = 'block';
        sliderContainer.style.display = 'none';

        // Filter posts if needed
        let posts = __config.posts || [];
        if (filterType && filterType !== 'all') {
            posts = posts.filter(post => post.type === filterType);
        }

        // Sort by date (newest first)
        posts.sort((a, b) => new Date(b.date) - new Date(a.date));

        // Apply limit if specified
        if (limit) {
            posts = posts.slice(0, limit);
        }

        // Clear existing cards
        cardsGrid.innerHTML = '';

        // Create cards
        posts.forEach(news => {
            const card = document.createElement('div');
            card.className = 'news-card';
            card.innerHTML = `
                <div class="news-card__image">
                    <img src="${news.img}" alt="${news.title}" loading="lazy">
                </div>
                <div class="news-card__content">
                    <div class="news-card__meta">
                        <span class="news-card__type">${news.type}</span>
                        <span class="news-card__date">${formatDate(news.date)}</span>
                    </div>
                    <h3 class="news-card__title">${news.title}</h3>
                    <p class="news-card__excerpt">${news.desc}</p>
                    <a href="${news.url}" class="news-card__button">Read More</a>
                </div>
            `;
            cardsGrid.appendChild(card);
        });

        if (posts.length === 0) {
            cardsGrid.innerHTML = '<div style="text-align: center; color: rgba(255,255,255,0.7); padding: 40px;">No articles found for this category.</div>';
        }
    }

    // Function to show slider view
    function showSliderView() {
        const cardsContainer = document.getElementById('newsCards');
        const sliderContainer = document.querySelector('.news__list');

        cardsContainer.style.display = 'none';
        sliderContainer.style.display = 'block';

        updateNewsDisplay();
    }

    // Helper function to format date
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('{{ $locale }}', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Load initial news and show as cards by default
        loadNews().then(() => {
            // Show 3 latest cards by default, sorted by date
            displayNewsCards('all', 3);
        });

        // Setup filter buttons
        const filterButtons = document.querySelectorAll('[data-sn-btn]');
        filterButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const filterType = this.getAttribute('data-sn-btn');

                // Update button states
                const allButtons = document.querySelectorAll('.news__btns .btn');
                allButtons.forEach(btn => {
                    btn.classList.remove('btn_accent');
                    btn.classList.add('btn_accent_no');
                });
                this.classList.remove('btn_accent_no');
                this.classList.add('btn_accent');

                // Display filtered content as cards
                displayNewsCards(filterType);
            });
        });

        // Handle "All" button click - let it navigate normally
        const allButton = document.querySelector('.news__btns a[href*="all-news"]');
        if (allButton) {
            allButton.addEventListener('click', function(e) {
                // Reset button states before navigation
                const allButtons = document.querySelectorAll('.news__btns .btn');
                allButtons.forEach(btn => {
                    btn.classList.remove('btn_accent');
                    btn.classList.add('btn_accent_no');
                });
                this.classList.remove('btn_accent_no');
                this.classList.add('btn_accent');

                // Let it navigate to all-news page (don't prevent default)
            });
        }
    });

    // Make functions globally available
    window.loadNews = loadNews;
    window.updateNewsDisplay = updateNewsDisplay;
})();
</script>
@endsection
