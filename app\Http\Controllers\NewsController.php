<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\News;

class NewsController extends Controller
{
    public function index(Request $request)
    {
        $locale = app()->getLocale();

        // Get paginated news for the current language
        $news = News::published()
            ->language($locale)
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('news.index', compact('news', 'locale'));
    }

    public function allNews(Request $request)
    {
        $locale = app()->getLocale();

        // Get all news for the current language (exclude features)
        $news = News::published()
            ->language($locale)
            ->whereIn('type', ['news', 'promotions'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('news.all', compact('news', 'locale'));
    }

    public function show(Request $request, $locale, $id)
    {
        $news = News::published()
            ->language($locale)
            ->findOrFail($id);

        // Get related news
        $relatedNews = News::published()
            ->language($locale)
            ->where('id', '!=', $id)
            ->take(3)
            ->get();

        return view('news.show', compact('news', 'relatedNews', 'locale'));
    }

    public function api(Request $request)
    {
        $locale = app()->getLocale();
        $limit = $request->get('limit', 10);
        $type = $request->get('type', 'all');

        $query = News::published()
            ->language($locale)
            ->orderBy('created_at', 'desc');

        if ($type !== 'all') {
            $query->where('type', $type);
        }

        $news = $query->take($limit)->get();

        // Format data to match original template structure
        $formattedNews = $news->map(function ($item) {
            return [
                'id' => $item->id,
                'title' => $item->title,
                'desc' => $item->excerpt ?: \Str::limit(strip_tags($item->content), 150),
                'content' => $item->content,
                'img' => $item->image ?: asset('template/site/l2gve/images/post/img-def-0.jpg'),
                'type' => $item->type,
                'date' => $item->created_at->format('Y-m-d H:i:s'),
                'url' => route('news.show', ['locale' => app()->getLocale(), 'id' => $item->id])
            ];
        });

        return response()->json($formattedNews);
    }
}
