# L2GVE Web - Đ<PERSON>ờng dẫn truy cập

## 🌐 Đường dẫn chính
Vì project được clone từ GitHub nên có cấu trúc folder kép:
```
Base URL: http://localhost/l2gve_web/l2gve_web/
```

## 📱 Các trang chính

### Trang chủ
- **Tiếng Việt**: `http://localhost/l2gve_web/l2gve_web/vn/index.html`
- **Tiếng Anh**: `http://localhost/l2gve_web/l2gve_web/en/index.html`

### Admin Panel
- **Quản lý tin tức**: `http://localhost/l2gve_web/l2gve_web/admin/news.php`
- **Admin chính**: `http://localhost/l2gve_web/l2gve_web/admin/index.php`

### API Endpoints
- **News API**: `http://localhost/l2gve_web/l2gve_web/api/news.php`
- **Test API**: `http://localhost/l2gve_web/l2gve_web/api/test.php`

### Test & Debug
- **Test Database**: `http://localhost/l2gve_web/l2gve_web/test-database.php`
- **Test API**: `http://localhost/l2gve_web/l2gve_web/test-api.html`
- **PHP Info**: `http://localhost/l2gve_web/l2gve_web/phpinfo.php`

## 🔧 API Usage Examples

### Lấy tất cả tin tức
```
GET http://localhost/l2gve_web/l2gve_web/api/news.php
```

### Lọc theo loại tin tức
```
GET http://localhost/l2gve_web/l2gve_web/api/news.php?type=news
GET http://localhost/l2gve_web/l2gve_web/api/news.php?type=promotions
```

### Giới hạn số lượng
```
GET http://localhost/l2gve_web/l2gve_web/api/news.php?limit=5
```

### Lấy tin tức theo ID
```
GET http://localhost/l2gve_web/l2gve_web/api/news.php?id=1
```

## 📊 Database Info
- **Host**: localhost
- **Database**: l2gve_web_db
- **Username**: root
- **Password**: 123456
- **Port**: 3306

## ✨ Tính năng đã cài đặt
- ✅ Back to Top button (mượt mà, chậm)
- ✅ News API kết nối MySQL
- ✅ Script loading tối ưu
- ✅ Admin panel quản lý tin tức
- ✅ Fallback data khi database lỗi
- ✅ CORS support cho API

## 🚀 Quick Start
1. Đảm bảo XAMPP đang chạy (Apache + MySQL)
2. Truy cập: `http://localhost/l2gve_web/l2gve_web/test-database.php`
3. Nếu database OK, truy cập trang chính: `http://localhost/l2gve_web/l2gve_web/vn/index.html`
4. Quản lý tin tức tại: `http://localhost/l2gve_web/l2gve_web/admin/news.php`
