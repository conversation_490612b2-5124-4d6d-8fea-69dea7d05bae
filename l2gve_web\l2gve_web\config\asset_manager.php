<?php
declare(strict_types=1);

require_once __DIR__ . '/asset_optimizer.php';

class AssetManager {
    private AssetOptimizer $optimizer;
    private array $cssFiles = [];
    private array $jsFiles = [];
    private array $preloadFiles = [];
    private bool $productionMode;
    
    public function __construct() {
        $this->optimizer = new AssetOptimizer();
        $this->productionMode = !($_ENV['DISABLE_DOMAIN_CHECK'] ?? false);
    }
    
    public function addCSS(string $file, int $priority = 10): self {
        $this->cssFiles[] = ['file' => $file, 'priority' => $priority];
        return $this;
    }
    
    public function addJS(string $file, int $priority = 10, bool $defer = true): self {
        $this->jsFiles[] = ['file' => $file, 'priority' => $priority, 'defer' => $defer];
        return $this;
    }
    
    public function addPreload(string $file, string $as = 'script'): self {
        $this->preloadFiles[] = ['file' => $file, 'as' => $as];
        return $this;
    }
    
    public function renderPreloadTags(): string {
        $html = '';
        foreach ($this->preloadFiles as $preload) {
            $html .= "<link rel=\"preload\" href=\"{$preload['file']}\" as=\"{$preload['as']}\">\n";
        }
        return $html;
    }
    
    public function renderCSSLinks(bool $inline = false): string {
        // Sort by priority
        usort($this->cssFiles, fn($a, $b) => $a['priority'] <=> $b['priority']);
        
        if ($this->productionMode) {
            return $this->renderOptimizedCSS($inline);
        }
        
        $html = '';
        foreach ($this->cssFiles as $css) {
            if ($inline && $this->isCriticalCSS($css['file'])) {
                $html .= $this->renderInlineCSS($css['file']);
            } else {
                $html .= "<link rel=\"stylesheet\" href=\"{$css['file']}\">\n";
            }
        }
        return $html;
    }
    
    public function renderJSScripts(): string {
        // Sort by priority
        usort($this->jsFiles, fn($a, $b) => $a['priority'] <=> $b['priority']);
        
        if ($this->productionMode) {
            return $this->renderOptimizedJS();
        }
        
        $html = '';
        foreach ($this->jsFiles as $js) {
            $defer = $js['defer'] ? ' defer' : '';
            $html .= "<script src=\"{$js['file']}\"{$defer}></script>\n";
        }
        return $html;
    }
    
    private function renderOptimizedCSS(bool $inline = false): string {
        $files = array_column($this->cssFiles, 'file');
        $bundle = $this->optimizer->generateAssetBundle($files, [], 'main');
        
        if ($inline) {
            $criticalCSS = $this->optimizer->generateCriticalCSS('', $files);
            return "<style>{$criticalCSS}</style>\n<link rel=\"preload\" href=\"{$bundle['css']}\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n";
        }
        
        return "<link rel=\"stylesheet\" href=\"{$bundle['css']}\">\n";
    }
    
    private function renderOptimizedJS(): string {
        $files = array_column($this->jsFiles, 'file');
        $bundle = $this->optimizer->generateAssetBundle([], $files, 'main');
        
        return "<script src=\"{$bundle['js']}\" defer></script>\n";
    }
    
    private function renderInlineCSS(string $file): string {
        $fullPath = $this->resolveFilePath($file);
        if (file_exists($fullPath)) {
            $css = file_get_contents($fullPath);
            $css = $this->optimizer->optimizeCSS($css);
            return "<style>{$css}</style>\n";
        }
        return '';
    }
    
    private function isCriticalCSS(string $file): bool {
        $criticalFiles = ['main.css', 'critical.css', 'above-fold.css'];
        return in_array(basename($file), $criticalFiles);
    }
    
    private function resolveFilePath(string $file): string {
        if (str_starts_with($file, '/')) {
            return __DIR__ . '/..' . $file;
        }
        if (str_starts_with($file, '../')) {
            return __DIR__ . '/../' . $file;
        }
        return $file;
    }
    
    public static function createDefault(): self {
        $manager = new self();
        
        // Add default assets with priorities
        $manager
            // Critical CSS first
            ->addCSS('../template/site/l2gve/css/critical.css', 1)
            ->addCSS('../template/site/l2gve/fonts/beaufortforlol/fonts.css', 2)
            ->addCSS('../template/site/l2gve/fonts/intro/stylesheet.css', 3)
            
            // Library CSS
            ->addCSS('../template/site/l2gve/libs/gwi/css/gwi.css', 5)
            ->addCSS('../template/site/l2gve/libs/fancybox/css/jquery.fancybox.min.css', 6)
            ->addCSS('../template/site/l2gve/libs/swiper/css/swiper.min.css', 7)
            
            // Main CSS
            ->addCSS('../template/site/l2gve/css/main.css', 8)
            ->addCSS('../template/site/l2gve/css/custom.css', 9)
            
            // Critical JS first
            ->addJS('../bypass-protection.js', 1, false)
            ->addJS('../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js', 2)
            
            // Library JS
            ->addJS('../template/site/l2gve/libs/js-cookie/js/js.cookie.min.js', 5)
            ->addJS('../template/site/l2gve/libs/fancybox/js/jquery.fancybox.js', 6)
            ->addJS('../template/site/l2gve/libs/swiper/js/swiper.min.js', 7)
            ->addJS('../template/site/l2gve/libs/gsap/js/gsap.min.js', 8)
            ->addJS('../template/site/l2gve/libs/gsap/js/ScrollTrigger.min.js', 9)
            ->addJS('../template/site/l2gve/libs/gsap/js/ScrollToPlugin.min.js', 10)
            
            // App JS
            ->addJS('../template/site/l2gve/js/app.js', 15)
            ->addJS('../template/site/l2gve/js/custom.js', 16);
            
        return $manager;
    }
}
