@extends('layouts.app')

@section('title', 'All News - L2GVE')
@section('description', 'All news and updates from L2GVE Essence GvE x100 server')

@section('styles')
<style>
    .all-news {
        padding: 100px 0 50px;
        min-height: 100vh;
    }

    .all-news__header {
        text-align: center;
        margin-bottom: 50px;
    }

    .all-news__title {
        font-size: 48px;
        color: #fff;
        margin-bottom: 20px;
        font-family: var(--font-1);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .all-news__filters {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-bottom: 40px;
        flex-wrap: wrap;
    }

    .all-news__content {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 30px;
        margin-bottom: 50px;
    }

    .news-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .news-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--color-accent), #00a8ff);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .news-card:hover {
        transform: translateY(-8px);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
        border-color: rgba(255, 255, 255, 0.25);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    }

    .news-card:hover::before {
        opacity: 1;
    }

    .news-card__image {
        width: 100%;
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    .news-card__image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .news-card:hover .news-card__image img {
        transform: scale(1.05);
    }

    .news-card__content {
        padding: 24px;
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 200px;
    }

    .news-card__title {
        background: #007cba;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        text-transform: uppercase;
        font-size: 11px;
        font-weight: 600;
        white-space: nowrap;
        display: inline-block;
        min-width: fit-content;
        width: auto;
        height: auto;
        max-width: none;
        flex: none;
        flex-shrink: 0;
        flex-grow: 0;
        flex-basis: auto;
        line-height: 1.2;
        margin: 10px 0;
    }

    .news-card__excerpt {
        color: rgba(255, 255, 255, 0.8);
        font-size: 15px;
        line-height: 1.6;
        margin-bottom: 18px;
        font-weight: 400;
    }

    .news-card__meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.6);
        margin-top: auto;
    }

    .news-card__type {
        background: linear-gradient(135deg, var(--color-accent), #0099cc);
        color: #fff;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 11px;
        text-transform: uppercase;
        font-weight: 700;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 8px rgba(0, 124, 186, 0.3);
        transition: all 0.3s ease;
    }

    .news-card:hover .news-card__type {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 124, 186, 0.5);
    }

    .news-card__date {
        font-weight: 500;
        color: rgba(255, 255, 255, 0.7);
    }

    .loading {
        text-align: center;
        padding: 50px;
        color: rgba(255, 255, 255, 0.7);
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 40px;
    }

    .pagination {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .pagination .page-link {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #fff;
        padding: 8px 12px;
        text-decoration: none;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .pagination .page-link:hover,
    .pagination .page-item.active .page-link {
        background: var(--color-accent);
        border-color: var(--color-accent);
    }

    @media (max-width: 768px) {
        .all-news__title {
            font-size: 32px;
        }
        
        .all-news__content {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .all-news__filters {
            gap: 10px;
        }
    }
</style>
@endsection

@section('content')
<!-- Background -->
<div class="bg">
    <img src="{{ asset('template/site/l2gve/images/bg/bg-repeat.jpg') }}" alt="bg" class="bg__img" aria-hidden="true" />
</div>

<div class="page">
    <section class="section compensate-for-scrollbar" data-section="all-news">
        <div class="container">
            <div class="all-news">
                <div class="navigation-height-compensate"></div>
                
                <div class="all-news__header" style="margin-top: -50px;">
                    <div class="all-news__filters">
                        <button class="btn btn_size_small btn_accent" data-filter="all">{{ __('messages.All') }}</button>
                        <button class="btn btn_size_small btn_accent_no" data-filter="news">{{ __('messages.News') }}</button>
                        <button class="btn btn_size_small btn_accent_no" data-filter="promotions">{{ __('messages.Promotions') }}</button>
                    </div>
                </div>

                <div class="all-news__content" id="newsContent">
                    @forelse($news as $item)
                        <div class="news-card" data-type="{{ $item->type }}" onclick="window.location.href='{{ route('news.show', ['locale' => $locale, 'id' => $item->id]) }}'">
                            <div class="news-card__image">
                                <img src="{{ $item->image ?: asset('template/site/l2gve/images/post/img-def-0.jpg') }}" alt="{{ $item->title }}" loading="lazy">
                            </div>
                            <div class="news-card__content">
                                <h3 class="news-card__title">{{ $item->title }}</h3>
                                <p class="news-card__excerpt">{{ $item->excerpt ?: Str::limit(strip_tags($item->content), 150) }}</p>
                                <div class="news-card__meta">
                                    <span class="news-card__type">{{ ucfirst($item->type) }}</span>
                                    <span class="news-card__date">{{ $item->created_at->format('M d, Y') }}</span>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="loading">
                            <p>{{ __('messages.No news available') }}</p>
                        </div>
                    @endforelse
                </div>

                @if($news->hasPages())
                    <div class="pagination-wrapper">
                        {{ $news->links() }}
                    </div>
                @endif
            </div>
        </div>
    </section>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('[data-filter]');
    const newsCards = document.querySelectorAll('.news-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filterType = this.getAttribute('data-filter');
            
            // Update button states
            filterButtons.forEach(btn => {
                btn.classList.remove('btn_accent');
                btn.classList.add('btn_accent_no');
            });
            this.classList.remove('btn_accent_no');
            this.classList.add('btn_accent');

            // Filter news cards
            newsCards.forEach(card => {
                const cardType = card.getAttribute('data-type');
                if (filterType === 'all' || cardType === filterType) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
});
</script>
@endsection
