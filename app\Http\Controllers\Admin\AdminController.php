<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;

class AdminController extends Controller
{
    public function index()
    {
        $stats = [
            'total_news' => News::count(),
            'published_news' => News::published()->count(),
            'draft_news' => News::where('status', 'inactive')->count(),
            'recent_news' => News::orderBy('created_at', 'desc')->take(5)->get()
        ];

        return view('admin.index', compact('stats'));
    }
}
