:root {
	/* fonts */
	--font-0: "BeaufortforLOL", "Times New Roman", Times, serif;
	--font-1: "Intro", "Open Sans", Tahoma, Arial, sans-serif;

	/* color palette */
	--color-0-rgb: rgb(255, 255, 255);
	--color-0: 255, 255, 255;

	--color-1-rgb: rgb(209, 209, 209);
	--color-1: 209, 209, 209;

	--color-2-rgb: rgb(255, 244, 225);
	--color-2: 255, 244, 225;

	--color-3-rgb: rgb(196, 184, 170);
	--color-3: 196, 184, 170;

	--color-accent-rgb: rgb(223, 198, 131);
	--color-accent: 223, 198, 131;

	--color-accent-hover-rgb: rgb(255, 229, 159);
	--color-accent-hover: 255, 229, 159;

	--color-accent-2-rgb: rgb(255, 229, 159);
	--color-accent-2: 255, 229, 159;

	--color-accent-2-hover-rgb: rgb(255, 229, 159);
	--color-accent-2-hover: 255, 229, 159;

	/* other colors */
	--color-url-rgb: rgb(255, 229, 159);
	--color-url: 255, 229, 159;

	--color-url-hover-rgb: rgb(255, 219, 118);
	--color-url-hover: 255, 219, 118;
}

body {
	background-color: rgba(15, 20, 22, 1);
	background-image: url(../media/bg/bg-repeat.jpg);
	background-size: max(100%, 93.75rem) auto;
	background-position: center top;
	background-repeat: repeat-y;
	min-height: 100vh;
	min-height: 100dvh;
	color: rgb(238, 238, 238);
	font-family: var(--font-0);
}

a {
	color: #bf9762;
}

a:hover {
	color: #f7c079;
}

#page-header {
	background-color: #131518 !important;
}

#page-container.page-header-modern #page-header > .content-header {
	padding-top: 0;
	padding-bottom: 0;
}

.nav-main-header a {
	color: #d8d8d8;
	background: rgba(74, 74, 74, 0.31);
	box-sizing: border-box;
	border: 1px solid transparent;
	transition: 0.3s all;
	font-weight: normal;
}

.nav-main-header a > i {
	color: rgba(var(--color-accent), 1);
}

.nav-main-header a:hover > i,
.nav-main-header .open a > i,
.nav-main-header a:focus > i {
	color: #000;
}

.btn-dual-secondary {
	color: #d8d8d8;
}

.text-dual-primary-dark {
	color: #d8d8d8 !important;
}

a.link-effect::before {
	background-color: #d8d8d8;
}

.nav-main-header a:hover,
.nav-main-header a:focus,
.nav-main-header a.active,
.nav-main-header li.open > a.nav-submenu,
.nav-main-header li:hover > a.nav-submenu {
	background-color: rgba(var(--color-accent), 1);
	border: 1px solid transparent;
	color: #000;
}

.btn-dual-secondary:not([disabled]):not(.disabled).active,
.show > .btn-dual-secondary.dropdown-toggle,
.btn-dual-secondary:hover,
.btn-dual-secondary:focus,
.btn-dual-secondary.focus {
	background-color: rgba(var(--color-accent), 1);
	border: 1px solid transparent;
	color: #000 !important;
}

.block {
	background-color: #343f44;
	border: 1px solid rgba(255, 255, 255, 0.05);
	box-sizing: border-box;
	box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
}

.block.block-shadow {
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.14);
}

.block-header {
	background: rgba(0, 0, 0, 0.24) !important;
}

.bg-body-light {
	background-color: rgba(0, 0, 0, 0.46) !important;
	border: 1px solid rgba(255, 255, 255, 0.05);
	box-sizing: border-box;
	box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
	border-top: 0;
}

a.text-muted:hover,
a.text-muted:focus {
	color: rgb(187, 187, 187) !important;
}

.submit-form {
	color: rgb(0, 0, 0);
	background-color: rgba(255, 255, 255, 0.46);
	border-color: rgba(0, 0, 0, 0);
	font-size: 15px;
}

.submit-form:hover {
	background-color: rgba(255, 255, 255, 0.7);
	color: rgb(0, 0, 0);
	border-color: rgba(0, 0, 0, 0);
}

.custom-control-input:checked ~ .custom-control-label::before {
	color: rgb(255, 255, 255);
	border-color: rgb(41, 145, 62);
	background-color: rgb(38, 166, 46);
}

.form-control {
	color: rgb(130, 130, 130);
	background-color: rgba(0, 0, 0, 0.44);
	border: 1px solid rgba(255, 255, 255, 0.05);
}

.form-control:focus {
	color: rgb(219, 219, 219);
	background-color: rgba(0, 0, 0, 0.6);
	border: 1px solid rgba(255, 255, 255, 0.05);
}

.form-control optgroup {
	background-color: rgba(255, 255, 255, 0.05);
	color: rgb(182, 182, 182);
}

.form-control option {
	background-color: rgb(27, 27, 27);
	color: rgb(219, 219, 219);
}

.nav-tabs-alt .nav-link.active,
.nav-tabs-alt .nav-item.show .nav-link {
	background-color: transparent;
	border-color: transparent;
	box-shadow: inset 0 -2px rgb(36, 36, 36);
	color: rgb(219, 219, 219);
}

.nav-link {
	color: #8c8c8c;
}

.nav-link:hover {
	color: rgba(var(--color-accent), 1);
}

.nav-tabs {
	border-bottom: 0;
}

.btn-secondary {
	color: rgb(130, 130, 130);
	background-color: rgba(0, 0, 0, 0.44);
	border: 1px solid rgba(255, 255, 255, 0.05);
}

.btn-secondary:not([disabled]):not(.disabled):active {
	color: rgb(219, 219, 219) !important;
	background-color: rgba(0, 0, 0, 0.6) !important;
	border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.breadcrumb-item {
	color: rgba(var(--color-accent), 1);
}

.breadcrumb-item:hover {
	color: #8b7655;
}

.breadcrumb-item.active {
	opacity: 0.4;
	color: #7d7d7d;
}

h1:not(.popover-header),
h2:not(.popover-header),
h3:not(.popover-header),
h4:not(.popover-header),
h5:not(.popover-header),
h6:not(.popover-header),
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
	color: rgba(var(--color-accent), 1) !important;
	font-family: var(--font-0);
}

.nav-tabs-alt .nav-link:hover,
.nav-tabs-alt .nav-link:focus {
	color: rgba(var(--color-accent), 1);
	box-shadow: inset 0 -2px rgba(var(--color-accent), 1);
}

.nav-tabs-block {
	background-color: rgba(246, 247, 249, 0.11);
}

.nav-tabs-block .nav-link:hover,
.nav-tabs-block .nav-link:focus {
	color: rgba(var(--color-accent), 1);
}

.table {
	color: #d9d9d9;
}

.table-hover tbody tr:hover {
	color: #fff;
	background-color: rgba(255, 255, 255, 0.15);
}

.block.block-bordered,
.table-bordered,
.table-bordered th,
.table-bordered td {
	border: 1px solid rgba(255, 255, 255, 0.05);
}

.table th,
.table td {
	border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.input-group-text {
	background-color: rgba(39, 37, 45, 0.94);
	border: 1px solid #383b3ea8;
	color: #a2a1a1;
}

.border-bottom {
	border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

#calculation_board {
	color: rgba(253, 253, 253, 0.45);
}

.dropdown-menu {
	background-color: rgba(53, 53, 53, 0.95);
	border: 1px solid transparent;
	border-radius: 5px;
}

.dropdown-item {
	color: #c8c8c8;
}

.dropdown-item:focus,
.dropdown-item:hover {
	color: #3b3b3b;
}

.nav-main-header ul,
.nav-main-header > li:hover > a.nav-submenu {
	background-color: rgba(var(--color-accent), 1);
	border: 1px solid transparent;
	color: #000;
}

.nav-main-header li a span,
.nav-main-header li a i {
	transition: 0.3s all;
}

.nav-main-header li ul li a {
	background-color: rgba(var(--color-accent), 1);
	color: #000;
}

.nav-main-header li ul li a i {
	color: #000 !important;
	transition: 0.3s all;
}

.nav-main-header > li ul li a:hover {
	background-color: #000;
	color: rgba(var(--color-accent), 1);
}

.nav-main-header li ul li a:hover i {
	color: rgba(var(--color-accent), 1) !important;
}

.dropdown-divider {
	border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.border-b {
	border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.block.block-fx-shadow {
	box-shadow: none;
}

.content .push {
	background-color: rgba(0, 0, 0, 0.11) !important;
}

.block .push {
	background-color: transparent !important;
}

.bg-info {
	background-color: rgba(38, 218, 193, 0.369) !important;
}

.content-heading {
	border-color: rgba(255, 255, 255, 0.05);
}

.alert-warning {
	color: #ffedcb;
	background-color: rgba(255, 220, 119, 0.271);
	border-color: rgb(255, 240, 195);
}

#page-container.page-header-modern #sidebar {
	box-shadow: 5px 0 20px #000;
}

#page-container #sidebar {
	color: #91a1c0;
	background-color: #0d141c;
}

#sidebar .nav-main a {
	color: rgba(216, 216, 216, 0.9);
	transition: 0.2s all;
}

#sidebar .nav-main a > i {
	color: rgba(216, 216, 216, 0.5);
	transition: 0.2s all;
}

#sidebar .nav-main a:hover,
#sidebar .nav-main a:focus,
#sidebar .nav-main a.active {
	color: rgba(var(--color-accent), 1);
}

#sidebar .nav-main a.active > i,
#sidebar .nav-main a:focus > i,
#sidebar .nav-main a:hover > i {
	color: rgba(var(--color-accent), 1);
}

.modal-content {
	background-color: #0d141c;
}

.border-right,
.border-left,
.border-top,
.border-bottom {
	border-color: rgba(222, 226, 230, 0.08) !important;
}

hr {
	border-color: rgba(222, 226, 230, 0.08) !important;
}

/* psa */

.psa {
	margin-top: 30px;
	margin-bottom: 30px;
}

.psa__title {
	margin-bottom: 10px;
	font-size: 18px;
	color: rgba(var(--color-accent), 1);
}

.psa__list {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;
	gap: 6px 10px;
}

.options-container .options-overlay {
	background-color: #121926 !important;
}

.btn.btn-rounded {
	border-radius: 5px !important;
}

.btn-alt-success {
	color: #0d0b0a;
	background-color: #ffc334;
	border-color: #ffc334;
}

.btn-alt-success:hover,
.btn-alt-success:focus,
.btn-alt-success.focus,
.btn-alt-success:not([disabled]):not(.disabled):active {
	color: #000000;
	background-color: #ffd779;
	border-color: #ffd779;
}

.btn-outline-primary {
	color: #a2866e;
	border-color: #5c361f73;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary.focus,
.btn-outline-primary:not([disabled]):not(.disabled):active {
	color: #f7e9c7;
	background-color: #53331b70;
	border-color: #5c361f73;
}

.btn-primary {
	color: #c7e7f7;
	background-color: #1b3a53;
	border-color: #1e4a6c;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary.focus,
.btn-primary:not([disabled]):not(.disabled):active {
	color: #e3f3fb;
	background-color: #326086;
	border-color: #246597;
}

.btn-alt-danger {
	color: #ee9f9e;
	background-color: #7b3633ab;
	border-color: #ffb2ae30;
}

.btn-alt-danger:hover,
.btn-alt-danger:focus,
.btn-alt-danger.focus,
.btn-alt-danger:not([disabled]):not(.disabled):active {
	color: #f4a6a4;
	background-color: #661a15;
	border-color: #7b2621;
}

.css-switch .css-control-input ~ .css-control-indicator {
	background-color: #535353;
}

.nav-tabs-block .nav-link.active,
.nav-tabs-block .nav-item.show .nav-link {
	color: rgba(var(--color-accent), 1);
	background-color: #aea88338;
	border-color: transparent;
}

.list-group-item {
	background-color: #5e5e5e3d;
	border: 1px solid #47494f80;
}

.list-group-item-action {
	color: #ccc;
}

.intl-tel-input .country-list {
	background-color: #1a1a1a;
	border-color: #464646;
}

.intl-tel-input .country-list .country.highlight {
	background-color: rgb(40, 40, 40);
}

.intl-tel-input .country-list .divider {
	border-color: #464646;
}

.modal-backdrop {
	background-color: #212528;
}

a.block.block-link-shadow:hover {
	/* box-shadow: 0 0 6px rgba(255, 255, 255, 0.150); */
	box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
	border-color: rgba(var(--color-accent), 1);
}

.nav-pills .nav-link.active,
.nav-pills .nav-link.active:hover,
.nav-pills .show > .nav-link,
.nav-pills .show > .nav-link:hover {
	background-color: rgba(var(--color-accent), 1);
	color: #000;
}

.isel {
	background-color: rgba(51, 51, 51, 0.44);
	margin-bottom: 4px;
	border: none;
}

.checkbox__block {
	background: rgba(255, 238, 145, 0.44);
	border-radius: 3px 0 0 3px;
	box-shadow: inset 2px 2px 10px rgba(0, 0, 0, 0.63);
	transform: scale(1);
}

.checkbox__block:after {
	border-radius: 3px 0 0 3px;
}

.checkbox__input:checked + .checkbox__block:after {
	background-color: rgb(41, 104, 41);
}

.content-header-item-logo {
	height: 40px;
	line-height: 40px;
}

/* itm */

.itm-list {
	display: grid;
	gap: 6px;
}

.itm {
	display: flex;
	align-items: center;
	box-sizing: border-box;
	margin-bottom: 6px;
}

.itm__pic {
	width: 30px;
	height: 30px;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	padding: 3px;
	flex-shrink: 0;
}

.itm__img {
	max-width: 100%;
	max-height: 100%;
}

.itm__content {
	font-style: normal;
	font-weight: normal;
	font-size: 12px;
	line-height: 12px;
	letter-spacing: 0.02em;
	color: rgb(255, 255, 255);
	margin-left: 6px;
}

/* logo */

.logo {
	display: flex;
	justify-content: center;
	align-items: center;
	text-decoration: none;
	font-size: 12px;
	position: relative;
	z-index: 0;
	max-width: 119px;
}

.logo__img {
	display: block;
	transition: 0.3s all;
	max-width: 100%;
}

.logo__img_hover_effect:hover {
	filter: brightness(120%);
}

.logo__img_hover_animation {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 2;
	opacity: 0;
	transition: 0.2s all;
	pointer-events: none;
}

.logo:hover .logo__img_hover_animation {
	animation: logo 0.3s ease;
}

@keyframes logo {
	0% {
		transform: scale(1);
		opacity: 0;
	}

	20% {
		opacity: 0.3;
	}

	100% {
		transform: scale(1.2);
		opacity: 0;
	}
}

.page-item.disabled .page-link {
	color: #999;
	pointer-events: none;
	cursor: auto;
	background-color: #202020;
	border-color: #404040;
}

.page-item.active .page-link {
	z-index: 1;
	color: rgba(var(--color-accent), 1);
	background-color: #2f2e2e;
	border-color: #4d4d4d;
}

a.block.block-link-pop:hover {
	box-shadow: none !important;
}

.fileuploader {
	background: #242424;
}

.fileuploader-theme-thumbnails .fileuploader-thumbnails-input-inner {
	background: #151515;
	border: 2px dashed #3e3e3e;
	text-align: center;
	font-size: 30px;
	color: #6a6a6a;
}

.text-primary {
	color: rgba(var(--color-accent), 1) !important;
}

.shop-item .text-muted {
	font-size: 18px;
	color: #dbdbbe !important;
}

.shop-item .shop-price {
	font-size: 18px;
	color: rgba(var(--color-accent), 1);
}

/* bns */

.bns__title {
	color: #ffb83c;
	font-size: 1.6rem;
	margin: 5px 0;
	text-align: center;
	font-family: var(--font-0);
}

.bns__desc {
	margin: 5px 0 10px;
	text-align: center;
}

.bns__list {
	display: grid;
	gap: 5px;
	max-width: 500px;
	margin: 0 auto;
}

.bns__row {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 5px;
}

.bns__box {
	display: grid;
	grid-template-columns: auto 1fr;
	gap: 3px;
	box-sizing: border-box;
	padding: 3px;
	background-color: rgba(252, 252, 252, 0.1);
	border-radius: 3px;
}

.bns__item {
	background: rgba(0, 0, 0, 0.3);
	padding: 5px 8px;
	border-radius: 3px;
	border: 1px solid transparent;
	box-sizing: border-box;
	line-height: 1.2;
	text-align: center;
	cursor: default;
	font-size: 14px;
}

.bns__item_accent {
	background-color: #ffb83c;
	color: #040404;
	font-weight: bold;
}

.bns__name {
	text-transform: uppercase;
}

.bns__val {
}
