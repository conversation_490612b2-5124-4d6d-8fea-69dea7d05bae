<?php
declare(strict_types=1);

interface CacheInterface {
    public function get(string $key): ?string;
    public function set(string $key, string $value, int $ttl): bool;
    public function delete(string $key): bool;
    public function clear(): bool;
    public function getMultiple(array $keys): array;
    public function setMultiple(array $values, int $ttl): bool;
}

class CacheManager implements CacheInterface {
    private static ?CacheInterface $instance = null;
    private string $cacheDir;
    private array $memoryCache = [];
    private int $maxMemoryItems = 1000;
    
    public function __construct(string $cacheDir = null) {
        $this->cacheDir = $cacheDir ?? __DIR__ . '/../cache';
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Create subdirectories for better organization
        $subdirs = ['pages', 'assets', 'api', 'templates', 'bundles'];
        foreach ($subdirs as $subdir) {
            $path = $this->cacheDir . '/' . $subdir;
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
        }
    }
    
    public static function getInstance(): CacheInterface {
        if (self::$instance === null) {
            // Try Redis first, fallback to file cache
            if (extension_loaded('redis') && class_exists('Redis')) {
                try {
                    self::$instance = new RedisCacheManager();
                } catch (Exception $e) {
                    self::$instance = new self();
                }
            } else {
                self::$instance = new self();
            }
        }
        return self::$instance;
    }
    
    public function get(string $key): ?string {
        // Check memory cache first
        if (isset($this->memoryCache[$key])) {
            $data = $this->memoryCache[$key];
            if ($data['expires'] > time()) {
                return $data['value'];
            }
            unset($this->memoryCache[$key]);
        }
        
        $file = $this->getCacheFile($key);
        if (!file_exists($file)) {
            return null;
        }
        
        $content = file_get_contents($file);
        if ($content === false) {
            return null;
        }
        
        $data = unserialize($content);
        if (!$data || $data['expires'] < time()) {
            unlink($file);
            return null;
        }
        
        // Store in memory cache
        $this->addToMemoryCache($key, $data['value'], $data['expires']);
        
        return $data['value'];
    }
    
    public function set(string $key, string $value, int $ttl): bool {
        $expires = time() + $ttl;
        $data = [
            'value' => $value,
            'expires' => $expires,
            'created' => time()
        ];
        
        // Store in memory cache
        $this->addToMemoryCache($key, $value, $expires);
        
        // Store in file cache
        $file = $this->getCacheFile($key);
        $dir = dirname($file);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        return file_put_contents($file, serialize($data), LOCK_EX) !== false;
    }
    
    public function delete(string $key): bool {
        unset($this->memoryCache[$key]);
        
        $file = $this->getCacheFile($key);
        if (file_exists($file)) {
            return unlink($file);
        }
        return true;
    }
    
    public function clear(): bool {
        $this->memoryCache = [];
        return $this->clearDirectory($this->cacheDir);
    }
    
    public function getMultiple(array $keys): array {
        $result = [];
        foreach ($keys as $key) {
            $result[$key] = $this->get($key);
        }
        return $result;
    }
    
    public function setMultiple(array $values, int $ttl): bool {
        $success = true;
        foreach ($values as $key => $value) {
            if (!$this->set($key, $value, $ttl)) {
                $success = false;
            }
        }
        return $success;
    }
    
    private function addToMemoryCache(string $key, string $value, int $expires): void {
        // Prevent memory cache from growing too large
        if (count($this->memoryCache) >= $this->maxMemoryItems) {
            // Remove oldest entries
            $oldest = array_slice($this->memoryCache, 0, 100, true);
            foreach ($oldest as $oldKey => $oldData) {
                unset($this->memoryCache[$oldKey]);
            }
        }
        
        $this->memoryCache[$key] = [
            'value' => $value,
            'expires' => $expires
        ];
    }
    
    private function getCacheFile(string $key): string {
        $hash = md5($key);
        $category = $this->getCacheCategory($key);
        return $this->cacheDir . '/' . $category . '/' . substr($hash, 0, 2) . '/' . $hash . '.cache';
    }
    
    private function getCacheCategory(string $key): string {
        if (str_starts_with($key, 'page_')) return 'pages';
        if (str_starts_with($key, 'css_') || str_starts_with($key, 'js_')) return 'assets';
        if (str_starts_with($key, 'api_')) return 'api';
        if (str_starts_with($key, 'template_')) return 'templates';
        if (str_starts_with($key, 'bundle_')) return 'bundles';
        return 'misc';
    }
    
    private function clearDirectory(string $dir): bool {
        if (!is_dir($dir)) {
            return true;
        }
        
        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($files as $fileinfo) {
            if ($fileinfo->isDir()) {
                rmdir($fileinfo->getRealPath());
            } else {
                unlink($fileinfo->getRealPath());
            }
        }
        
        return true;
    }
}

class RedisCacheManager implements CacheInterface {
    private Redis $redis;
    private string $prefix;

    public function __construct(string $host = '127.0.0.1', int $port = 6379, string $prefix = 'l2gve:') {
        $this->redis = new Redis();
        $this->prefix = $prefix;

        if (!$this->redis->connect($host, $port, 2.0)) {
            throw new RuntimeException('Cannot connect to Redis server');
        }

        // Set Redis options for better performance
        $this->redis->setOption(Redis::OPT_SERIALIZER, Redis::SERIALIZER_PHP);
        $this->redis->setOption(Redis::OPT_COMPRESSION, Redis::COMPRESSION_LZ4);
    }

    public function get(string $key): ?string {
        $value = $this->redis->get($this->prefix . $key);
        return $value === false ? null : $value;
    }

    public function set(string $key, string $value, int $ttl): bool {
        return $this->redis->setex($this->prefix . $key, $ttl, $value);
    }

    public function delete(string $key): bool {
        return $this->redis->del($this->prefix . $key) > 0;
    }

    public function clear(): bool {
        $keys = $this->redis->keys($this->prefix . '*');
        if (empty($keys)) {
            return true;
        }
        return $this->redis->del($keys) > 0;
    }

    public function getMultiple(array $keys): array {
        $prefixedKeys = array_map(fn($key) => $this->prefix . $key, $keys);
        $values = $this->redis->mget($prefixedKeys);

        $result = [];
        foreach ($keys as $i => $key) {
            $result[$key] = $values[$i] === false ? null : $values[$i];
        }

        return $result;
    }

    public function setMultiple(array $values, int $ttl): bool {
        $pipe = $this->redis->multi(Redis::PIPELINE);

        foreach ($values as $key => $value) {
            $pipe->setex($this->prefix . $key, $ttl, $value);
        }

        $results = $pipe->exec();
        return !in_array(false, $results, true);
    }

    public function increment(string $key, int $value = 1): int {
        return $this->redis->incrBy($this->prefix . $key, $value);
    }

    public function decrement(string $key, int $value = 1): int {
        return $this->redis->decrBy($this->prefix . $key, $value);
    }

    public function exists(string $key): bool {
        return $this->redis->exists($this->prefix . $key) > 0;
    }

    public function expire(string $key, int $ttl): bool {
        return $this->redis->expire($this->prefix . $key, $ttl);
    }
}
