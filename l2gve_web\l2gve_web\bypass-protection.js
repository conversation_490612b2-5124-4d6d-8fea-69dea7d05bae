// Add this script before other scripts in index.html
(function() {
    // Check if we're in local development
    const isLocal = window.location.hostname === 'localhost' ||
                   window.location.hostname === '127.0.0.1' ||
                   window.location.hostname.includes('local');

    if (isLocal) {
        console.log('Local development mode - domain protection disabled');

        // Store original location
        const originalLocation = window.location;

        // Create a safe location proxy
        const locationProxy = new Proxy(originalLocation, {
            get: function(target, prop) {
                if (prop === 'hostname') {
                    return 'l2gve.com'; // Fake hostname for local dev
                }
                return target[prop];
            },
            set: function(target, prop, value) {
                if (prop === 'href' && typeof value === 'string' && value.includes('get-web.site/protection')) {
                    console.log('Domain protection bypassed for local development');
                    return true;
                }
                target[prop] = value;
                return true;
            }
        });

        // Set global flag for domain protection bypass
        window.DISABLE_DOMAIN_CHECK = true;
        window.LOCAL_DEV_MODE = true;

        // Override common domain check patterns
        window.originalLocation = originalLocation;

        // Intercept common redirect patterns
        const originalSetTimeout = window.setTimeout;
        window.setTimeout = function(callback, delay) {
            if (typeof callback === 'function') {
                const callbackStr = callback.toString();
                if (callbackStr.includes('get-web.site') || callbackStr.includes('protection')) {
                    console.log('Domain protection redirect blocked for local development');
                    return;
                }
            }
            return originalSetTimeout.apply(this, arguments);
        };

        // Block Cookiebot for localhost
        window.Cookiebot = {
            consent: {
                marketing: true,
                statistics: true,
                preferences: true,
                necessary: true
            },
            show: function() { console.log('Cookiebot blocked for localhost'); },
            hide: function() { console.log('Cookiebot blocked for localhost'); },
            renew: function() { console.log('Cookiebot blocked for localhost'); }
        };

        // Block Cookiebot script loading
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(this, tagName);
            if (tagName.toLowerCase() === 'script') {
                const originalSetAttribute = element.setAttribute;
                element.setAttribute = function(name, value) {
                    if (name === 'src' && value && value.includes('cookiebot')) {
                        console.log('Cookiebot script blocked for localhost');
                        return;
                    }
                    return originalSetAttribute.call(this, name, value);
                };
            }
            return element;
        };
    }
})();