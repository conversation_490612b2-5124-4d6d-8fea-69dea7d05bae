<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tải Game - L2GVE Essence GvE x100</title>
    <meta name="description" content="Tải game client L2GVE, patches và hướng dẫn cài đặt cho trải nghiệm Lineage II tuyệt vời nhất.">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="../template/site/l2gve/images/favicon/favicon.ico" type="image/x-icon">
    
    <!-- Fonts -->
    <link rel="stylesheet" href="../template/site/l2gve/fonts/beaufortforlol/fonts.css" />
    <link rel="stylesheet" href="../template/site/l2gve/fonts/intro/stylesheet.css" />
    
    <!-- CSS -->
    <link rel="stylesheet" href="../template/site/l2gve/libs/gwi/css/gwi.css" />
    <link rel="stylesheet" href="../template/site/l2gve/libs/fancybox/css/jquery.fancybox.min.css" />
    <link rel="stylesheet" href="../template/site/l2gve/css/main.css@v=1719174774.css">
    <link rel="stylesheet" href="../template/site/l2gve/css/custom.css@v=1714752167.css">

    <style>
        .download-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #0f1416 0%, #1a2328 100%);
            padding: 2rem 0;
        }
        
        .download-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .download-header {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .download-title {
            font-size: 3rem;
            color: #ffffff;
            font-family: 'BeaufortforLOL', sans-serif;
            margin-bottom: 1rem;
        }
        
        .download-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }
        
        .download-card {
            background: rgba(15, 20, 22, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .download-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 215, 0, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .download-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            display: block;
        }
        
        .download-card-title {
            color: #ffffff;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            font-family: 'BeaufortforLOL', sans-serif;
        }
        
        .download-card-desc {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .download-btn {
            display: inline-block;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #000;
            padding: 1rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .download-btn:hover {
            background: linear-gradient(135deg, #ffed4e, #ffd700);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
        }
        
        .download-btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .download-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 215, 0, 0.5);
        }
        
        .system-requirements {
            background: rgba(15, 20, 22, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .requirements-title {
            color: #ffffff;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            font-family: 'BeaufortforLOL', sans-serif;
            text-align: center;
        }

        .requirements-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 12px;
            overflow: hidden;
        }

        .requirements-table th {
            background: linear-gradient(135deg, #d4af37, #ffd700);
            color: #000;
            padding: 15px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .requirements-table td {
            padding: 12px 15px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 13px;
        }

        .requirements-table tr:nth-child(even) {
            background: rgba(255, 255, 255, 0.02);
        }

        .requirements-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .requirements-table .spec-category {
            background: rgba(255, 255, 255, 0.05);
            font-weight: 600;
            color: #d4af37;
        }

        .requirements-table .minimum-spec {
            color: #ff6b6b;
        }

        .requirements-table .recommended-spec {
            color: #51cf66;
        }
        
        .requirements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .requirement-item {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .requirement-label {
            color: rgba(255, 215, 0, 0.8);
            font-weight: 500;
            min-width: 120px;
        }
        
        .requirement-value {
            color: rgba(255, 255, 255, 0.8);
        }
        
        /* Language navigation enhancement */
        .lang__current .lang__name {
            background: #007cba !important;
            color: white !important;
            padding: 4px 8px !important;
            border-radius: 4px !important;
            font-weight: bold !important;
        }

        .lang__link .lang__name {
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .lang__link:hover .lang__name {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .download-title {
                font-size: 2rem;
            }

            .download-grid {
                grid-template-columns: 1fr;
            }

            .requirements-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="section compensate-for-scrollbar" data-section="navigation">
    <div class="container" data-container="navigation">
        <div class="navigation">
            <div class="navigation__box navigation__box_side_left">
                <a href="index" class="logo">
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img" style="width: 9rem" />
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img logo__img_hover" />
                </a>

            </div>

           <div class="navigation__menu menu" data-menu>
    <div class="menu__content">
        <ul class="menu__list">
            <li class="menu__el">
                <a href="index" class="menu__item" data-menu-close>
                    Trang chủ
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="all-news" class="menu__item" data-menu-close>
                    Tin tức
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="features" class="menu__item" data-menu-close>
                    Tính năng
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="download" class="menu__item" data-menu-close>
                    Tải game
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="#discord" class="menu__item" data-menu-close>
                    Discord
                </a>
            </li>
            <li class="menu__el menu__el_continer menu__el_desktop_none" data-place-from="scl">
                <div class="scl" data-place-container="scl">
                    <div class="scl__list">
                        <a href="index.html#Telegram" target="_blank" class="scl__item">
                            <i class="gwi gwi_c-telegram scl__ico-c-telegram"></i>
                        </a>
                        <a href="index.html#Discord" target="_blank" class="scl__item">
                            <i class="gwi gwi_discord scl__ico-discord"></i>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>            <div class="navigation__box navigation__box_side_right" data-place-to="auth">
                <div class="navigation__lang lang notranslate">
    <div class="lang__current">
        <div class="lang__name" data-current-lang>vn</div>
    </div>
    <div class="lang__list">
        <a href="download" class="lang__link lang__link_sub" data-google-lang="vi">
            <div class="lang__name">vn</div>
        </a>
        <a href="../en/download" class="lang__link lang__link_sub" data-google-lang="en">
            <div class="lang__name">en</div>
        </a>
    </div>
</div>



            </div>

            <div class="navigation__gw-burger gw-burger" data-gw-burger>
                <div class="gw-burger__box">
                    <div class="gw-burger__line gw-burger__line_pos_top"></div>
                    <div class="gw-burger__line gw-burger__line_pos_middle"></div>
                    <div class="gw-burger__line gw-burger__line_pos_bottom"></div>
                </div>
            </div>
        </div>
        <!-- END  navigation -->
    </div>
</div>
<!-- END  section -->

    <div class="download-page">
        <div class="download-container">
            <div style="height: 100px;"></div> <!-- Navigation height compensate -->
            <!-- Header -->
            <div class="download-header">
                <h1 class="download-title">Tải Game L2GVE</h1>
                <p class="download-subtitle">Tải tất cả những gì bạn cần để bắt đầu cuộc phiêu lưu Lineage II trên máy chủ Essence GvE x100 của chúng tôi</p>
            </div>
            
            <!-- Download Options -->
            <div class="download-grid">
                <div class="download-card">
                    <div class="download-icon">💾</div>
                    <h3 class="download-card-title">Game Client</h3>
                    <p class="download-card-desc">Game client L2GVE đầy đủ với tất cả các file cần thiết và bản cập nhật mới nhất.</p>
                    <a href="javascript:void(0)" onclick="downloadFile('client')" class="download-btn">Tải Client (2.5 GB)</a>
                </div>

                <div class="download-card">
                    <div class="download-icon">🔄</div>
                    <h3 class="download-card-title">Bản Vá Mới Nhất</h3>
                    <p class="download-card-desc">Cập nhật client hiện tại với các bản vá và sửa lỗi mới nhất.</p>
                    <a href="javascript:void(0)" onclick="downloadFile('patch')" class="download-btn download-btn-secondary">Tải Patch (150 MB)</a>
                </div>

                <div class="download-card">
                    <div class="download-icon">📋</div>
                    <h3 class="download-card-title">Hướng Dẫn Cài Đặt</h3>
                    <p class="download-card-desc">Hướng dẫn từng bước để cài đặt và cấu hình game client.</p>
                    <a href="javascript:void(0)" onclick="showInstallGuide()" class="download-btn download-btn-secondary">Xem Hướng Dẫn</a>
                </div>
            </div>
            
            <!-- System Requirements -->
            <div class="system-requirements">
                <h3 class="requirements-title">Cấu Hình Yêu Cầu</h3>
                <table class="requirements-table">
                    <thead>
                        <tr>
                            <th>Phần cứng</th>
                            <th>Cấu hình tối thiểu</th>
                            <th>Cấu hình đề nghị</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="spec-category">CPU</td>
                            <td class="minimum-spec">Intel® Core™ i3-3240</td>
                            <td class="recommended-spec">Intel® Core™ i5-7400 hoặc cao hơn</td>
                        </tr>
                        <tr>
                            <td class="spec-category">Memory</td>
                            <td class="minimum-spec">4 GB</td>
                            <td class="recommended-spec">8 GB hoặc cao hơn</td>
                        </tr>
                        <tr>
                            <td class="spec-category">Video Card</td>
                            <td class="minimum-spec">NVIDIA® GeForce® 7600 GT hoặc ATI / AMD Radeon™ X1800 512MB</td>
                            <td class="recommended-spec">NVIDIA® GeForce® GTX 750 hoặc ATI / AMD Radeon™ R7-250</td>
                        </tr>
                        <tr>
                            <td class="spec-category">Online</td>
                            <td colspan="2">ADSL hoặc cao hơn</td>
                        </tr>
                        <tr>
                            <td class="spec-category">Hard Disk</td>
                            <td colspan="2">Trống 40GB trở lên</td>
                        </tr>
                        <tr>
                            <td class="spec-category">Direct X</td>
                            <td colspan="2">Direct X 9.0c hoặc cao hơn</td>
                        </tr>
                        <tr>
                            <td class="spec-category">Operating System</td>
                            <td colspan="2">Windows 7 32bit / 64bit Windows 7 / Windows 8 32bit / Windows 8 64bit / Windows 10</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            

        </div>
    </div>

    <script>
        // Download configuration
        const downloadLinks = {
            client: 'https://drive.google.com/file/d/1234567890/view?usp=sharing', // Replace with actual link
            patch: 'https://drive.google.com/file/d/0987654321/view?usp=sharing',  // Replace with actual link
            guide: '../docs/installation-guide.pdf' // Replace with actual guide
        };

        function downloadFile(type) {
            const link = downloadLinks[type];

            if (!link) {
                alert('Link download chưa được cấu hình!');
                return;
            }

            // Show download starting message
            const messages = {
                client: 'Đang bắt đầu tải Game Client...',
                patch: 'Đang bắt đầu tải Patch...'
            };

            if (messages[type]) {
                alert(messages[type]);
            }

            // Open download link
            window.open(link, '_blank');

            // Track download (optional)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'download', {
                    'event_category': 'file',
                    'event_label': type,
                    'value': 1
                });
            }
        }

        function showInstallGuide() {
            // Create modal for installation guide
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    max-width: 600px;
                    max-height: 80vh;
                    overflow-y: auto;
                    position: relative;
                ">
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        position: absolute;
                        top: 10px;
                        right: 15px;
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                    ">×</button>

                    <h2 style="color: #333; margin-bottom: 20px;">Hướng Dẫn Cài Đặt L2GVE</h2>

                    <div style="color: #666; line-height: 1.6;">
                        <h3>Bước 1: Tải Game Client</h3>
                        <p>• Click nút "Tải Client" để tải file game đầy đủ</p>
                        <p>• Dung lượng: khoảng 2.5 GB</p>

                        <h3>Bước 2: Giải nén</h3>
                        <p>• Giải nén file đã tải vào thư mục mong muốn</p>
                        <p>• Khuyến nghị: C:\\Games\\L2GVE\\</p>

                        <h3>Bước 3: Chạy Updater</h3>
                        <p>• Chạy file "L2GVE_Updater.exe"</p>
                        <p>• Đợi cập nhật hoàn tất</p>

                        <h3>Bước 4: Tạo tài khoản</h3>
                        <p>• Truy cập website để đăng ký tài khoản</p>
                        <p>• Xác nhận email</p>

                        <h3>Bước 5: Chơi game</h3>
                        <p>• Chạy "L2GVE.exe"</p>
                        <p>• Đăng nhập và bắt đầu phiêu lưu!</p>

                        <div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin-top: 20px;">
                            <strong>Lưu ý:</strong> Đảm bảo tắt antivirus khi cài đặt để tránh bị chặn file game.
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Auto-check for updates
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Download page loaded');

            // You can add version checking here
            // checkForUpdates();
        });
    </script>

    <!-- Scripts -->
    <script src="../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js"></script>
    <script src="../template/site/l2gve/js/clean-app.js"></script>
    <script src="../template/site/l2gve/js/custom.js@v=1714752167"></script>
</body>
</html>
