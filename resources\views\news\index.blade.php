@extends('layouts.app')

@section('title', __('messages.News') . ' - L2GVE')
@section('description', 'Latest news and updates from L2GVE Lineage 2 server')

@section('content')
<section class="section" data-section="news" data-target-section="news" data-target-section-offset="0" id="news">
    <div class="container" data-container="news">
        <div class="news" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="news__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h1 class="heading__title">{{ __('messages.News') }}</h1>
                <div class="heading__dec">
                    <img src="{{ asset('template/site/l2gve/images/heading/dec.png') }}" alt="decoration" class="heading__dec-img">
                </div>
            </div>
            
            <div class="news__content">
                @if($news->count() > 0)
                    <div class="news__list">
                        @foreach($news as $newsItem)
                        <article class="post">
                            <div class="post__bg" style="background-image: url('{{ $newsItem->image ?: asset('template/site/l2gve/images/post/img-def-0.jpg') }}')"></div>
                            <div class="post__container">
                                <div class="post__date">
                                    <span class="post__date-accent">{{ $newsItem->created_at->format('d') }}</span>
                                    {{ $newsItem->created_at->format('M Y') }}
                                </div>
                                <h2 class="post__title">{{ $newsItem->title }}</h2>
                                <div class="post__desc">{{ $newsItem->excerpt }}</div>
                                <div class="post__btns">
                                    <a href="{{ route('news.show', ['locale' => $locale, 'id' => $newsItem->id]) }}" class="btn">
                                        {{ __('messages.Read More') }}
                                    </a>
                                </div>
                            </div>
                            <a href="{{ route('news.show', ['locale' => $locale, 'id' => $newsItem->id]) }}" class="post__link"></a>
                        </article>
                        @endforeach
                    </div>
                    
                    <!-- Pagination -->
                    <div class="news__pagination">
                        {{ $news->links() }}
                    </div>
                @else
                    <div class="news__empty">
                        <p>{{ __('messages.No news available at the moment.') }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
// News page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Add any news-specific functionality here
});
</script>
@endsection
