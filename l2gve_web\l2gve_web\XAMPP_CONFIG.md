# XAMPP Configuration for L2GVE PHP 8.4 Optimization

## 🔧 Enable OPcache in XAMPP

### Step 1: Edit php.ini
1. Open XAMPP Control Panel
2. Click "Config" next to Apache
3. Select "PHP (php.ini)"
4. Find the line `;extension=opcache` and remove the semicolon:
   ```ini
   extension=opcache
   ```

### Step 2: Add OPcache Configuration
Add these lines at the end of php.ini:

```ini
[opcache]
; Enable OPcache
opcache.enable=1
opcache.enable_cli=1

; Memory settings
opcache.memory_consumption=512
opcache.interned_strings_buffer=64
opcache.max_accelerated_files=50000

; Performance settings
opcache.revalidate_freq=0
opcache.validate_timestamps=1
opcache.save_comments=0
opcache.enable_file_override=1

; JIT settings (PHP 8.0+)
opcache.jit_buffer_size=512M
opcache.jit=1255

; Optimization
opcache.optimization_level=0x7FFEBFFF

; Development settings (for XAMPP)
opcache.fast_shutdown=1
opcache.max_wasted_percentage=10
```

### Step 3: Restart Apache
1. Stop Apache in XAMPP Control Panel
2. Start Apache again
3. Check if OPcache is working by visiting: `http://localhost/l2gve_web/l2gve_web/setup.php`

## 🚀 Alternative: Quick Test Without OPcache

If you want to test the site without OPcache first:
1. Visit: `http://localhost/l2gve_web/l2gve_web/test.php`
2. The site will work but with reduced performance

## 📝 Troubleshooting

### If you get "Forbidden" errors:
1. Check if index.php files exist in /en/ and /vn/ directories
2. Make sure Apache has read permissions on the directories

### If you get PHP errors:
1. Check XAMPP error logs in: `C:\xampp\apache\logs\error.log`
2. Make sure PHP version is 8.0 or higher

### Performance Issues:
1. Enable OPcache as described above
2. Consider upgrading to PHP 8.4 for best performance
3. Use SSD storage for better I/O performance
