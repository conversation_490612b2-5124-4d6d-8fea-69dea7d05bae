@font-face {
  font-family: 'gwi';
  src: url('../font/gwi.eot@99126122');
  src: url('../font/gwi.eot@99126122') format('embedded-opentype'),
       url('../font/gwi.woff2@99126122') format('woff2'),
       url('../font/gwi.woff@99126122') format('woff'),
       url('../font/gwi.ttf@99126122') format('truetype'),
       url('../font/gwi.svg@99126122') format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'gwi';
    src: url('../font/gwi.svg?99126122#gwi') format('svg');
  }
}
*/
[class^="gwi_"]:before, [class*=" gwi_"]:before {
  font-family: "gwi";
  font-style: normal;
  font-weight: normal;
  speak: never;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;

  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */

  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.gwi_ok-2:before { content: '\e101'; } /* '' */
.gwi_up:before { content: '\e800'; } /* '' */
.gwi_right:before { content: '\e801'; } /* '' */
.gwi_left:before { content: '\e802'; } /* '' */
.gwi_down:before { content: '\e803'; } /* '' */
.gwi_mail:before { content: '\e804'; } /* '' */
.gwi_location-outline:before { content: '\e805'; } /* '' */
.gwi_network:before { content: '\e806'; } /* '' */
.gwi_edit:before { content: '\e807'; } /* '' */
.gwi_share:before { content: '\e808'; } /* '' */
.gwi_eye:before { content: '\e809'; } /* '' */
.gwi_c-arrow-left:before { content: '\e80a'; } /* '' */
.gwi_comment-alt:before { content: '\e80b'; } /* '' */
.gwi_clock:before { content: '\e80c'; } /* '' */
.gwi_c-user:before { content: '\e80d'; } /* '' */
.gwi_c-arrow-right:before { content: '\e80e'; } /* '' */
.gwi_doc-landscape:before { content: '\e80f'; } /* '' */
.gwi_ok:before { content: '\e810'; } /* '' */
.gwi_left-open:before { content: '\e811'; } /* '' */
.gwi_right-open:before { content: '\e812'; } /* '' */
.gwi_discord:before { content: '\e813'; } /* '' */
.gwi_skype:before { content: '\e814'; } /* '' */
.gwi_up-open:before { content: '\e815'; } /* '' */
.gwi_down-open:before { content: '\e816'; } /* '' */
.gwi_logout:before { content: '\e817'; } /* '' */
.gwi_c-telegram:before { content: '\e818'; } /* '' */
.gwi_c-arrow-left-open:before { content: '\e819'; } /* '' */
.gwi_c-arrow-right-open:before { content: '\e81a'; } /* '' */
.gwi_lock-filled:before { content: '\e81b'; } /* '' */
.gwi_arrows-cw:before { content: '\e81c'; } /* '' */
.gwi_c-setting:before { content: '\e81d'; } /* '' */
.gwi_c-protect:before { content: '\e81e'; } /* '' */
.gwi_th:before { content: '\e81f'; } /* '' */
.gwi_th-large:before { content: '\e820'; } /* '' */
.gwi_plus:before { content: '\e821'; } /* '' */
.gwi_minus:before { content: '\e822'; } /* '' */
.gwi_minus-1:before { content: '\e823'; } /* '' */
.gwi_c-book:before { content: '\e824'; } /* '' */
.gwi_c-pad:before { content: '\e825'; } /* '' */
.gwi_user:before { content: '\e826'; } /* '' */
.gwi_users:before { content: '\e827'; } /* '' */
.gwi_picture:before { content: '\e828'; } /* '' */
.gwi_status-hot:before { content: '\e829'; } /* '' */
.gwi_stream-ico:before { content: '\e82a'; } /* '' */
.gwi_video-play:before { content: '\e82b'; } /* '' */
.gwi_c-download:before { content: '\e82c'; } /* '' */
.gwi_ok-1:before { content: '\e82d'; } /* '' */
.gwi_c-check:before { content: '\e82e'; } /* '' */
.gwi_info-circled:before { content: '\e82f'; } /* '' */
.gwi_calendar:before { content: '\e830'; } /* '' */
.gwi_calendar-1:before { content: '\e831'; } /* '' */
.gwi_ccw:before { content: '\e832'; } /* '' */
.gwi_eye-1:before { content: '\e833'; } /* '' */
.gwi_eye-off:before { content: '\e834'; } /* '' */
.gwi_asg-world:before { content: '\e83c'; } /* '' */
.gwi_asg-user:before { content: '\e842'; } /* '' */
.gwi_i-info:before { content: '\e843'; } /* '' */
.gwi_i-download:before { content: '\e844'; } /* '' */
.gwi_i-calendar:before { content: '\e845'; } /* '' */
.gwi_mlti-lock:before { content: '\e848'; } /* '' */
.gwi_signal:before { content: '\e852'; } /* '' */
.gwi_checked-circle:before { content: '\e853'; } /* '' */
.gwi_lock:before { content: '\e854'; } /* '' */
.gwi_user-setting:before { content: '\e855'; } /* '' */
.gwi_user-lock:before { content: '\e857'; } /* '' */
.gwi_c-cart:before { content: '\e867'; } /* '' */
.gwi_c-cart-plus:before { content: '\e868'; } /* '' */
.gwi_search:before { content: '\e900'; } /* '' */
.gwi_list:before { content: '\e901'; } /* '' */
.gwi_paper-plane:before { content: '\f01d'; } /* '' */
.gwi_login:before { content: '\f02c'; } /* '' */
.gwi_link-ext:before { content: '\f08e'; } /* '' */
.gwi_twitter:before { content: '\f099'; } /* '' */
.gwi_facebook:before { content: '\f09a'; } /* '' */
.gwi_comment-empty:before { content: '\f0e5'; } /* '' */
.gwi_download-cloud:before { content: '\f0ed'; } /* '' */
.gwi_pencil-squared:before { content: '\f14b'; } /* '' */
.gwi_sort-number-down:before { content: '\f163'; } /* '' */
.gwi_youtube:before { content: '\f167'; } /* '' */
.gwi_youtube-play:before { content: '\f16a'; } /* '' */
.gwi_instagram:before { content: '\f16d'; } /* '' */
.gwi_down-1:before { content: '\f175'; } /* '' */
.gwi_up-1:before { content: '\f176'; } /* '' */
.gwi_left-1:before { content: '\f177'; } /* '' */
.gwi_right-1:before { content: '\f178'; } /* '' */
.gwi_vkontakte:before { content: '\f189'; } /* '' */
.gwi_clone:before { content: '\f24d'; } /* '' */
.gwi_user-circle:before { content: '\f2bd'; } /* '' */
.gwi_user-circle-o:before { content: '\f2be'; } /* '' */
.gwi_user-o:before { content: '\f2c0'; } /* '' */
